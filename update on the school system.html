 <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline School Grade Management System (Extended Placeholder)</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 0;
            display: flex;
            min-height: 100vh;
            background-color: #f4f4f4;
            color: #333;
        }
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding-top: 20px;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            flex-shrink: 0; /* Prevent sidebar from shrinking */
             overflow-y: auto; /* Allow scrolling if menu is long */
        }
        .sidebar h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #ecf0f1;
            font-size: 1.4em;
        }
        .sidebar ul {
            list-style: none;
            padding: 0;
        }
        .sidebar ul li {
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            border-bottom: 1px solid #3a536e;
        }
         .sidebar ul li:last-child {
             border-bottom: none;
         }
        .sidebar ul li:hover {
            background-color: #34495e;
        }
        .sidebar ul li.active {
            background-color: #1abc9c;
        }
        .main-content {
            flex-grow: 1;
            padding: 20px;
            overflow-y: auto; /* Allow main content to scroll */
        }
        .section {
            display: none; /* Hide sections by default */
            background-color: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .section h2 {
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 15px;
            margin-bottom: 20px;
            font-size: 1.6em;
        }

        /* Form Styling */
        form {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 5px;
        }
         form h3 {
             margin-top: 0;
             color: #2c3e50;
         }
        form label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        form input[type="text"],
        form input[type="number"],
        form input[type="date"],
        form select,
        form textarea { /* Added textarea for potential comments */
            width: calc(100% - 22px);
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 1em;
        }
        form textarea {
            resize: vertical; /* Allow vertical resizing */
            min-height: 80px;
        }
         form button {
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            font-size: 1em;
            margin-right: 10px; /* Add space between buttons */
        }
         form button.cancel {
            background-color: #95a5a6;
         }
         form button:hover {
            background-color: #2980b9;
        }
         form button.cancel:hover {
            background-color: #7f8c8d;
         }

        /* Data List/Table Styling */
        .data-list {
            margin-top: 20px;
        }
        .data-list h3 {
            margin-top: 0;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .data-list ul {
            list-style: none;
            padding: 0;
        }
        .data-list ul li {
            background-color: #ecf0f1;
            padding: 12px;
            margin-bottom: 8px;
            border-radius: 4px;
            display: flex; /* Use flexbox for layout */
            justify-content: space-between; /* Put content on left, buttons on right */
            align-items: center; /* Vertically center items */
             flex-wrap: wrap; /* Allow wrapping on smaller screens */
        }
         .list-item-info {
             flex-grow: 1; /* Allow info area to take available space */
             margin-right: 10px; /* Space between info and buttons */
         }
         .list-item-actions button {
             padding: 5px 10px;
             font-size: 0.9em;
             margin-left: 5px;
             cursor: pointer;
             border: none;
             border-radius: 3px;
         }
         .list-item-actions button.edit { background-color: #f39c12; color: white; }
         .list-item-actions button.edit:hover { background-color: #e67e22; }
         .list-item-actions button.delete { background-color: #e74c3c; color: white; }
         .list-item-actions button.delete:hover { background-color: #c0392b; }


         table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        table th, table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        table th {
            background-color: #e0e0e0;
            font-weight: bold;
             color: #555;
        }
         table tr:nth-child(even) {
             background-color: #f9f9f9;
         }
          table td button { /* Styling for edit/delete inside table */
             padding: 4px 8px;
             font-size: 0.8em;
             margin-right: 5px;
             cursor: pointer;
             border: none;
             border-radius: 3px;
         }
          table td button.edit { background-color: #f39c12; color: white; }
          table td button.edit:hover { background-color: #e67e22; }
          table td button.delete { background-color: #e74c3c; color: white; }
          table td button.delete:hover { background-color: #c0392b; }

        /* Helper Classes */
        .hidden {
            display: none;
        }
        .form-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        .form-buttons button {
            margin-right: 0; /* Remove margin from flex items */
        }

        /* Loading/Status */
         #status-message {
             margin-top: 20px;
             padding: 10px;
             border-radius: 5px;
         }
         .status-success {
             background-color: #d4edda;
             color: #155724;
             border: 1px solid #c3e6cb;
         }
          .status-error {
             background-color: #f8d7da;
             color: #721c24;
             border: 1px solid #f5c6cb;
         }
          .status-info {
             background-color: #d1ecf1;
             color: #0c5460;
             border: 1px solid #bee5eb;
         }

         /* Report Output Styling */
         #report-output {
             margin-top: 30px;
             padding: 15px;
             background-color: #fff;
             border: 1px solid #ddd;
             border-radius: 5px;
             white-space: pre-wrap; /* Preserve line breaks and spaces */
             font-family: monospace;
             font-size: 0.9em;
             max-height: 400px; /* Limit height */
             overflow-y: auto; /* Add scrollbar */
         }

    </style>
</head>
<body>

    <div class="sidebar">
        <h2>Grade System</h2>
        <ul>
            <li data-section="dashboard" class="active">Dashboard</li>
            <li data-section="school-info">School Info</li>
            <li data-section="classes">Classes</li>
            <li data-section="subjects">Subjects</li>
            <li data-section="students">Students</li>
            <li data-section="grade-entry">Enter Grades</li>
            <li data-section="view-grades">View Grades</li>
            <li data-section="reports">Reports</li>
            <li data-section="fees">Fees</li>
            <li data-section="users">Users</li>
        </ul>
    </div>

    <div class="main-content">

        <div id="status-message" class="status-info hidden"></div>

        <div id="dashboard" class="section" style="display: block;">
            <h2>Dashboard</h2>
            <p>Welcome to the Offline School Grade Management System.</p>
            <p>This prototype uses IndexedDB for local storage and includes basic CRUD for core entities. Placeholders for Reports, Fees, and Users sections have been added.</p>
            <div class="announcements">
                <h3>Announcements</h3>
                <ul>
                    <li>System initialized. Data is stored securely in your browser's IndexedDB.</li>
                    <li>Do not clear your browser's site data for this origin if you want to keep your information.</li>
                    <li>Basic Add, Edit, Delete are available for core entities.</li>
                    <li>Placeholder sections for Reports, Fees, and Users now contain structural elements.</li>
                </ul>
            </div>
        </div>

        <div id="school-info" class="section">
            <h2>School Information</h2>
            <form id="school-info-form">
                <input type="hidden" id="school-info-id" value="singleton"> <label for="school-name">School Name:</label>
                <input type="text" id="school-name" name="schoolName" required>

                <label for="school-address">Address:</label>
                <input type="text" id="school-address" name="schoolAddress">

                 <label for="school-year">Academic Year:</label>
                <input type="text" id="school-year" name="schoolYear">

                <button type="submit">Save School Info</button>
            </form>
             <div class="data-list" id="current-school-info">
                 <h3>Current Info</h3>
                 <p>Name: <span id="display-school-name">Loading...</span></p>
                 <p>Address: <span id="display-school-address">Loading...</span></p>
                 <p>Academic Year: <span id="display-school-year">Loading...</span></p>
            </div>
        </div>

        <div id="classes" class="section">
            <h2>Classes</h2>
             <form id="class-form">
                <input type="hidden" id="class-id"> <label for="class-name">Class Name (e.g., Grade 1, Grade 7):</label>
                <input type="text" id="class-name" required>
                <div class="form-buttons">
                    <button type="submit" id="add-class-btn">Add Class</button>
                     <button type="submit" id="update-class-btn" class="hidden">Update Class</button>
                    <button type="button" id="cancel-class-edit" class="cancel hidden">Cancel</button>
                </div>
            </form>
            <div class="data-list">
                <h3>Existing Classes</h3>
                <ul id="classes-list">
                    <li>Loading...</li>
                </ul>
            </div>
        </div>

        <div id="subjects" class="section">
            <h2>Subjects</h2>
             <form id="subject-form">
                <input type="hidden" id="subject-id"> <label for="subject-name">Subject Name (e.g., Mathematics, English):</label>
                <input type="text" id="subject-name" required>
                 <div class="form-buttons">
                    <button type="submit" id="add-subject-btn">Add Subject</button>
                     <button type="submit" id="update-subject-btn" class="hidden">Update Subject</button>
                    <button type="button" id="cancel-subject-edit" class="cancel hidden">Cancel</button>
                </div>
            </form>
             <div class="data-list">
                <h3>Existing Subjects</h3>
                <ul id="subjects-list">
                     <li>Loading...</li>
                </ul>
            </div>
             <p><em>Note: Linking subjects to specific grades as per documentation is a more advanced feature not included in this prototype. Subjects added here are general.</em></p>
        </div>

        <div id="students" class="section">
            <h2>Students</h2>
             <form id="student-form">
                <input type="hidden" id="student-id"> <label for="student-name">Student Name:</label>
                <input type="text" id="student-name" required>

                 <label for="student-class">Assign to Class:</label>
                 <select id="student-class" required>
                     <option value="">-- Select Class --</option>
                     </select>
                <div class="form-buttons">
                     <button type="submit" id="add-student-btn">Add Student</button>
                     <button type="submit" id="update-student-btn" class="hidden">Update Student</button>
                    <button type="button" id="cancel-student-edit" class="cancel hidden">Cancel</button>
                </div>
            </form>
            <div class="data-list">
                <h3>Existing Students</h3>
                <ul id="students-list">
                     <li>Loading...</li>
                </ul>
            </div>
        </div>

         <div id="grade-entry" class="section">
            <h2>Enter / Edit Grades</h2>
             <form id="grade-form">
                 <input type="hidden" id="grade-id"> <label for="grade-class">Select Class:</label>
                 <select id="grade-class" required>
                      <option value="">-- Select Class --</option>
                      </select>

                  <label for="grade-student">Select Student:</label>
                 <select id="grade-student" required disabled>
                      <option value="">-- Select Student --</option>
                      </select>

                 <label for="grade-subject">Select Subject:</label>
                 <select id="grade-subject" required>
                      <option value="">-- Select Subject --</option>
                       </select>

                  <label for="grade-period">Select Period:</label>
                 <select id="grade-period" required>
                      <option value="">-- Select Period --</option>
                      <option value="1ST P">1ST P</option>
                      <option value="2ND P">2ND P</option>
                      <option value="3RD P">3RD P</option>
                      <option value="EXAM 1">EXAM 1</option>
                      <option value="AV1">AV1 (Periods 1-3 + Exam 1)</option>
                       <option value="4TH P">4TH P</option>
                      <option value="5TH P">5TH P</option>
                      <option value="6TH P">6TH P</option>
                      <option value="EXAM 2">EXAM 2</option>
                      <option value="AV2">AV2 (Periods 4-6 + Exam 2)</option>
                       <option value="FINAL AVE">FINAL AVE (AV1 + AV2 / 2)</option>
                 </select>

                 <label for="student-grade">Grade:</label>
                 <input type="number" id="student-grade" min="0" max="100" required>

                 <label for="student-comment">Comment:</label>
                 <input type="text" id="student-comment">
                <div class="form-buttons">
                     <button type="submit" id="add-grade-btn">Save Grade</button>
                     <button type="submit" id="update-grade-btn" class="hidden">Update Grade</button>
                    <button type="button" id="cancel-grade-edit" class="cancel hidden">Cancel</button>
                </div>
             </form>
              <p><em>Note: Grade averages (AV1, AV2, FINAL AVE) are not calculated automatically in this prototype. Enter them manually if needed.</em></p>
        </div>

        <div id="view-grades" class="section">
            <h2>View Grades</h2>
            <form id="filter-grades-form">
                 <label for="filter-class">Filter by Class:</label>
                 <select id="filter-class">
                      <option value="">-- All Classes --</option>
                       </select>

                 <label for="filter-student">Filter by Student:</label>
                 <select id="filter-student" disabled>
                      <option value="">-- All Students --</option>
                       </select>

                 <label for="filter-subject">Filter by Subject:</label>
                 <select id="filter-subject">
                      <option value="">-- All Subjects --</option>
                       </select>

                  <label for="filter-period">Filter by Period:</label>
                 <select id="filter-period">
                      <option value="">-- All Periods --</option>
                       <option value="1ST P">1ST P</option>
                      <option value="2ND P">2ND P</option>
                      <option value="3RD P">3RD P</option>
                      <option value="EXAM 1">EXAM 1</option>
                      <option value="AV1">AV1</option>
                       <option value="4TH P">4TH P</option>
                      <option value="5TH P">5TH P</option>
                      <option value="6TH P">6TH P</option>
                      <option value="EXAM 2">EXAM 2</option>
                      <option value="AV2">AV2</option>
                       <option value="FINAL AVE">FINAL AVE</option>
                 </select>
                <div class="form-buttons">
                    <button type="submit">Apply Filter</button>
                     <button type="button" id="reset-filter" class="cancel">Reset Filter</button>
                </div>
            </form>
            <div class="data-list">
                <h3>Grades List</h3>
                <table id="grades-table">
                    <thead>
                        <tr>
                            <th>Class</th>
                            <th>Student</th>
                            <th>Subject</th>
                            <th>Period</th>
                            <th>Grade</th>
                            <th>Comment</th>
                             <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td colspan="7">Loading grades...</td></tr>
                    </tbody>
                </table>
                 <p id="no-grades-message" class="hidden">No grades found matching filters.</p>
            </div>
        </div>

        <div id="reports" class="section">
            <h2>Reports (Placeholder)</h2>
            <p>Select criteria and click a button to simulate generating a report based on available data.</p>

            <form id="report-options-form">
                <h3>Report Options</h3>
                 <label for="report-class">Select Class (for Class Reports):</label>
                 <select id="report-class">
                      <option value="">-- Select Class --</option>
                       </select>

                 <label for="report-student">Select Student (for Student Reports):</label>
                 <select id="report-student">
                      <option value="">-- Select Student --</option>
                       </select>

                 <div class="form-buttons">
                     <button type="button" id="generate-reportcard">Generate Report Card (Placeholder)</button>
                     <button type="button" id="generate-gradesheet">Generate Class Grade Sheet (Placeholder)</button>
                     <button type="button" id="generate-studentprogress">Generate Student Progress (Placeholder)</button>
                      <button type="button" id="generate-classperformance">Generate Class Performance (Placeholder)</button>
                 </div>
            </form>

            <div class="data-list">
                <h3>Report Output (Placeholder)</h3>
                 <pre id="report-output">Select report options and click a button to see placeholder output.</pre>
            </div>

            <p><em>Note: This section simulates report generation by displaying raw or simply formatted data. Full report card layouts, detailed progress reports, and aggregate calculations (like class averages) are complex features not included.</em></p>
        </div>

        <div id="fees" class="section">
            <h2>Fees (Placeholder)</h2>

             <form id="fee-setup-form">
                 <h3>Fee Setup</h3>
                <input type="hidden" id="fee-id">
                 <label for="fee-name">Fee Name (e.g., Tuition, Books):</label>
                 <input type="text" id="fee-name" required>

                 <label for="fee-amount">Amount:</label>
                 <input type="number" id="fee-amount" step="0.01" min="0" required>

                 <label for="fee-assigned-class">Assign to Class (Optional):</label>
                 <select id="fee-assigned-class">
                      <option value="">-- All Classes (or select specific) --</option>
                      </select>
                 <div class="form-buttons">
                     <button type="submit" id="add-fee-btn">Add Fee</button>
                     <button type="submit" id="update-fee-btn" class="hidden">Update Fee</button>
                    <button type="button" id="cancel-fee-edit" class="cancel hidden">Cancel</button>
                 </div>
            </form>

            <div class="data-list">
                <h3>Defined Fees</h3>
                 <ul id="fees-list">
                    <li>Loading...</li>
                 </ul>
            </div>

             <hr style="margin: 30px 0;">

             <form id="fee-payment-form">
                 <h3>Record Payment</h3>
                 <input type="hidden" id="payment-id">
                 <label for="payment-student">Select Student:</label>
                 <select id="payment-student" required>
                     <option value="">-- Select Student --</option>
                     </select>

                 <label for="payment-fee">Select Fee:</label>
                 <select id="payment-fee" required>
                      <option value="">-- Select Fee Type --</option>
                      </select>

                 <label for="payment-amount">Amount Paid:</label>
                 <input type="number" id="payment-amount" step="0.01" min="0" required>

                 <label for="payment-date">Payment Date:</label>
                 <input type="date" id="payment-date" required>

                 <label for="payment-notes">Notes:</label>
                 <textarea id="payment-notes"></textarea>

                 <div class="form-buttons">
                     <button type="submit" id="add-payment-btn">Record Payment</button>
                     <button type="submit" id="update-payment-btn" class="hidden">Update Payment</button>
                     <button type="button" id="cancel-payment-edit" class="cancel hidden">Cancel</button>
                 </div>
             </form>

             <div class="data-list">
                 <h3>Payment Records</h3>
                 <ul id="payments-list">
                     <li>Loading...</li>
                 </ul>
             </div>

             <hr style="margin: 30px 0;">

             <div class="data-list">
                 <h3>Fee Summary (Placeholder)</h3>
                 <p>Balance tracking, fee statements, and comprehensive fee reports are not implemented in this prototype.</p>
                 <p>You can add fee types and record payments above.</p>
             </div>
        </div>

         <div id="users" class="section">
             <h2>User Management (Placeholder)</h2>
             <p>Manage different user accounts for the system.</p>

             <form id="user-form">
                 <h3>Add User</h3>
                 <input type="hidden" id="user-id">
                 <label for="user-name">Name:</label>
                 <input type="text" id="user-name" required>

                  <label for="user-type">User Type:</label>
                 <select id="user-type" required>
                      <option value="">-- Select Type --</option>
                      <option value="Administrator">Administrator</option>
                      <option value="Teacher">Teacher</option>
                      <option value="Student">Student</option>
                      <option value="Parent">Parent</option>
                 </select>

                 <p><em>Note: Linking Teachers to Classes or Parents to Students, and implementing authentication/permissions are advanced features not included.</em></p>

                 <div class="form-buttons">
                     <button type="submit" id="add-user-btn">Add User</button>
                     <button type="submit" id="update-user-btn" class="hidden">Update User</button>
                     <button type="button" id="cancel-user-edit" class="cancel hidden">Cancel</button>
                 </div>
            </form>

             <div class="data-list">
                <h3>Existing Users</h3>
                <ul id="users-list">
                    <li>Loading...</li>
                </ul>
            </div>
         </div>

    </div>

    <script>
        // --- IndexedDB Setup ---
        const DB_NAME = 'SchoolGradeSystemDB';
        // Increment version to trigger upgrade and add new stores
        const DB_VERSION = 2;
        let db;

        const STORES = {
            SCHOOL_INFO: 'schoolInfo',
            CLASSES: 'classes',
            SUBJECTS: 'subjects',
            STUDENTS: 'students',
            GRADES: 'grades',
            FEES: 'fees',       // New store
            PAYMENTS: 'payments', // New store
            USERS: 'users'      // New store
        };

        // Helper function to open the database
        function openDatabase() {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open(DB_NAME, DB_VERSION);

                request.onupgradeneeded = function(event) {
                    db = event.target.result;
                    console.log(`Database upgrade needed to version ${event.newVersion}, creating stores...`);

                    // Create object stores if they don't exist
                    if (!db.objectStoreNames.contains(STORES.SCHOOL_INFO)) {
                        db.createObjectStore(STORES.SCHOOL_INFO, { keyPath: 'id' });
                    }
                     if (!db.objectStoreNames.contains(STORES.CLASSES)) {
                        db.createObjectStore(STORES.CLASSES, { keyPath: 'id', autoIncrement: true });
                    }
                     if (!db.objectStoreNames.contains(STORES.SUBJECTS)) {
                        db.createObjectStore(STORES.SUBJECTS, { keyPath: 'id', autoIncrement: true });
                    }
                     if (!db.objectStoreNames.contains(STORES.STUDENTS)) {
                        const studentStore = db.createObjectStore(STORES.STUDENTS, { keyPath: 'id', autoIncrement: true });
                        studentStore.createIndex('byClass', 'classId', { unique: false });
                    }
                     if (!db.objectStoreNames.contains(STORES.GRADES)) {
                        const gradeStore = db.createObjectStore(STORES.GRADES, { keyPath: 'id', autoIncrement: true });
                         gradeStore.createIndex('byStudent', 'studentId', { unique: false });
                         gradeStore.createIndex('bySubject', 'subjectId', { unique: false });
                         gradeStore.createIndex('byPeriod', 'period', { unique: false });
                    }

                    // --- Add New Stores in Version 2 ---
                    if (!db.objectStoreNames.contains(STORES.FEES)) {
                         db.createObjectStore(STORES.FEES, { keyPath: 'id', autoIncrement: true });
                         // Could add index by assignedClassId if needed
                    }
                    if (!db.objectStoreNames.contains(STORES.PAYMENTS)) {
                         const paymentStore = db.createObjectStore(STORES.PAYMENTS, { keyPath: 'id', autoIncrement: true });
                         paymentStore.createIndex('byStudent', 'studentId', { unique: false });
                         paymentStore.createIndex('byFee', 'feeId', { unique: false });
                         paymentStore.createIndex('byDate', 'paymentDate', { unique: false }); // Index by date string
                    }
                    if (!db.objectStoreNames.contains(STORES.USERS)) {
                         const userStore = db.createObjectStore(STORES.USERS, { keyPath: 'id', autoIncrement: true });
                         userStore.createIndex('byType', 'type', { unique: false });
                    }

                    console.log('Database upgrade complete. Stores created.');
                };

                request.onsuccess = function(event) {
                    db = event.target.result;
                    console.log('Database opened successfully');
                    resolve(db);
                };

                request.onerror = function(event) {
                    console.error('Database error:', event.target.errorCode);
                    showStatusMessage('Error opening database: ' + event.target.error, 'error');
                    reject(event.target.error);
                };
            });
        }

        // Helper function for database transactions
        function transaction(storeNames, mode) {
            // Ensure storeNames is an array
            const storeNamesArray = Array.isArray(storeNames) ? storeNames : [storeNames];
            const tx = db.transaction(storeNamesArray, mode);
             const stores = storeNamesArray.reduce((acc, name) => {
                 acc[name] = tx.objectStore(name);
                 return acc;
             }, {});


            return {
                stores: stores, // Return an object of stores if multiple names provided
                 store: stores[storeNamesArray[0]], // For backward compatibility/simplicity if only one name
                complete: new Promise((resolve, reject) => {
                    tx.oncomplete = () => resolve();
                    tx.onerror = (event) => {
                         console.error(`Transaction error on ${storeNamesArray.join(',')}:`, event.target.error);
                         showStatusMessage(`Database transaction error: ${event.target.error}`, 'error');
                         reject(event.target.error);
                     };
                     tx.onabort = (event) => {
                        console.warn(`Transaction aborted on ${storeNamesArray.join(',')}:`, event.target.error);
                        showStatusMessage(`Database transaction aborted: ${event.target.error}`, 'error');
                        reject(event.target.error);
                     };
                })
            };
        }

        // --- Generic Database Operations ---

        async function addData(storeName, data) {
            const { store, complete } = transaction(storeName, 'readwrite');
            // Ensure data has a place for ID if using autoIncrement, but don't set it
             if (store.autoIncrement) {
                 // IndexedDB handles ID for autoIncrement
             } else if (data.id === undefined) {
                  // For stores not using autoIncrement but needing a keyPath, ensure ID is set
                 if (storeName === STORES.SCHOOL_INFO && !data.id) data.id = 'singleton'; // Special case
             }
            store.add(data);
            await complete;
             console.log(`Added data to ${storeName}`, data);
        }

         async function getAllData(storeName) {
             const { store, complete } = transaction(storeName, 'readonly');
             const request = store.getAll();
              return new Promise((resolve, reject) => {
                  request.onsuccess = (event) => resolve(event.target.result);
                  request.onerror = (event) => reject(event.target.error);
                  complete.catch(reject); // Ensure transaction errors are caught
              });
         }

         async function getDataById(storeName, id) {
             const { store, complete } = transaction(storeName, 'readonly');
             const request = store.get(id);
              return new Promise((resolve, reject) => {
                  request.onsuccess = (event) => resolve(event.target.result);
                  request.onerror = (event) => reject(event.target.error);
                  complete.catch(reject);
              });
         }

         async function updateData(storeName, data) {
            const { store, complete } = transaction(storeName, 'readwrite');
             // Ensure data has the ID for put operation
             if (data.id === undefined) {
                 const error = new Error(`Cannot update data in ${storeName}: data object must have an 'id'.`);
                 console.error(error);
                 throw error;
             }
            store.put(data); // Use put for update
            await complete;
             console.log(`Updated data in ${storeName}`, data);
         }

         async function deleteData(storeName, id) {
            const { store, complete } = transaction(storeName, 'readwrite');
            store.delete(id);
            await complete;
             console.log(`Deleted data from ${storeName} with id ${id}`);
         }

        // --- Data Cache ---
        let cache = {
            schoolInfo: null,
            classes: [],
            subjects: [],
            students: [],
            grades: [],
            fees: [],       // Add new cache keys
            payments: [],   // Add new cache keys
            users: []       // Add new cache keys
        };

        async function refreshCache(storeName) {
             try {
                // School info is a singleton, handle separately
                 if (storeName === STORES.SCHOOL_INFO) {
                      const data = await getDataById(STORES.SCHOOL_INFO, 'singleton');
                      cache[storeName] = data ? [data] : null; // Store as array for consistency or null
                 } else {
                    cache[storeName] = await getAllData(storeName);
                 }

                 console.log(`Cache refreshed for ${storeName}`, cache[storeName]);
             } catch (error) {
                 console.error(`Failed to refresh cache for ${storeName}`, error);
                 showStatusMessage(`Failed to load data for ${storeName}.`, 'error', 5000);
                 // Set cache to default empty state on error
                 cache[storeName] = (storeName === STORES.SCHOOL_INFO) ? null : [];
             }
        }

        async function refreshAllCaches() {
             showStatusMessage('Loading all data...', 'info', 0);
             await Promise.all([
                 refreshCache(STORES.SCHOOL_INFO),
                 refreshCache(STORES.CLASSES),
                 refreshCache(STORES.SUBJECTS),
                 refreshCache(STORES.STUDENTS),
                 refreshCache(STORES.GRADES),
                 refreshCache(STORES.FEES),
                 refreshCache(STORES.PAYMENTS),
                 refreshCache(STORES.USERS),
             ]);
             showStatusMessage('All data loaded.', 'success');
        }


        // --- UI Rendering ---

        const statusMessageDiv = document.getElementById('status-message');
        function showStatusMessage(message, type = 'info', duration = 5000) {
            // Clear previous timeout
             if (statusMessageDiv._timeoutId) {
                clearTimeout(statusMessageDiv._timeoutId);
             }

            statusMessageDiv.textContent = message;
            statusMessageDiv.className = `status-message status-${type}`;
            statusMessageDiv.classList.remove('hidden');

            // Hide after duration
            if (duration > 0) {
                statusMessageDiv._timeoutId = setTimeout(() => {
                    statusMessageDiv.classList.add('hidden');
                    statusMessageDiv._timeoutId = null;
                }, duration);
            } else {
                 // No timeout, keep visible
                 statusMessageDiv._timeoutId = null;
            }
        }

        async function renderSchoolInfo() {
            const info = cache.schoolInfo ? cache.schoolInfo[0] : null; // Assuming only one school info object
            // Use 'singleton' key for saving if no data exists
            document.getElementById('school-info-id').value = info ? info.id : 'singleton';

            document.getElementById('school-name').value = info?.schoolName || '';
            document.getElementById('school-address').value = info?.schoolAddress || '';
             document.getElementById('school-year').value = info?.schoolYear || '';

             document.getElementById('display-school-name').textContent = info?.schoolName || 'N/A';
             document.getElementById('display-school-address').textContent = info?.schoolAddress || 'N/A';
             document.getElementById('display-school-year').textContent = info?.schoolYear || 'N/A';
        }

        async function renderClasses() {
            const classesList = document.getElementById('classes-list');
            classesList.innerHTML = ''; // Clear list
            if (cache.classes.length === 0) {
                 classesList.innerHTML = '<li>No classes added yet.</li>';
                 return;
            }
            cache.classes.forEach(cls => {
                const li = document.createElement('li');
                li.innerHTML = `
                    <span class="list-item-info">${escapeHTML(cls.name)}</span>
                    <span class="list-item-actions">
                        <button class="edit" data-id="${cls.id}">Edit</button>
                        <button class="delete" data-id="${cls.id}">Delete</button>
                    </span>
                `;
                classesList.appendChild(li);
            });
        }

         async function renderSubjects() {
            const subjectsList = document.getElementById('subjects-list');
            subjectsList.innerHTML = ''; // Clear list
             if (cache.subjects.length === 0) {
                 subjectsList.innerHTML = '<li>No subjects added yet.</li>';
                 return;
            }
            cache.subjects.forEach(sub => {
                const li = document.createElement('li');
                 li.innerHTML = `
                    <span class="list-item-info">${escapeHTML(sub.name)}</span>
                    <span class="list-item-actions">
                        <button class="edit" data-id="${sub.id}">Edit</button>
                        <button class="delete" data-id="${sub.id}">Delete</button>
                    </span>
                `;
                subjectsList.appendChild(li);
            });
        }

         async function renderStudents() {
            const studentsList = document.getElementById('students-list');
            studentsList.innerHTML = ''; // Clear list
             if (cache.students.length === 0) {
                 studentsList.innerHTML = '<li>No students added yet.</li>';
                 return;
            }
            cache.students.forEach(student => {
                const className = cache.classes.find(c => c.id == student.classId)?.name || 'Unassigned'; // Use == for potential type mismatch
                const li = document.createElement('li');
                 li.innerHTML = `
                    <span class="list-item-info">${escapeHTML(student.name)} (Class: ${escapeHTML(className)})</span>
                    <span class="list-item-actions">
                        <button class="edit" data-id="${student.id}">Edit</button>
                        <button class="delete" data-id="${student.id}">Delete</button>
                    </span>
                `;
                studentsList.appendChild(li);
            });
        }

        function populateClassDropdowns(selectedValue = '') {
             const classDropdowns = document.querySelectorAll('select[id$="-class"]'); // Selects all selects whose ID ends with -class
             classDropdowns.forEach(dropdown => {
                 const currentSelected = dropdown.value; // Store current value before clearing
                 dropdown.innerHTML = '<option value="">-- Select Class --</option>';
                  cache.classes.forEach(cls => {
                     const option = document.createElement('option');
                     option.value = cls.id;
                     option.textContent = cls.name;
                     dropdown.appendChild(option);
                 });
                  // Restore previous selection or set provided selectedValue
                  dropdown.value = selectedValue || currentSelected;
                  // Manually trigger change if value was set programmatically and is valid
                  if (dropdown.value && dropdown.value != currentSelected) { // Use != for potential type mismatch
                      dropdown.dispatchEvent(new Event('change'));
                  }
             });
        }

         function populateSubjectDropdowns(selectedValue = '') {
             const subjectDropdowns = document.querySelectorAll('select[id$="-subject"]'); // Selects all selects whose ID ends with -subject
              subjectDropdowns.forEach(dropdown => {
                 const currentSelected = dropdown.value;
                 dropdown.innerHTML = '<option value="">-- Select Subject --</option>';
                  cache.subjects.forEach(sub => {
                     const option = document.createElement('option');
                     option.value = sub.id;
                     option.textContent = sub.name;
                     dropdown.appendChild(option);
                 });
                 dropdown.value = selectedValue || currentSelected;
                 if (dropdown.value && dropdown.value != currentSelected) {
                      dropdown.dispatchEvent(new Event('change'));
                  }
              });
         }

         function populateStudentDropdown(dropdownId, classId, selectedValue = '') {
             const studentDropdown = document.getElementById(dropdownId);
             const currentSelected = studentDropdown.value;
             studentDropdown.innerHTML = '<option value="">-- Select Student --</option>';

             const studentsToDisplay = classId
                 ? cache.students.filter(student => student.classId == classId) // Use == for potential type coercion with IDs
                 : cache.students; // If no classId, show all students

              studentsToDisplay.forEach(student => {
                 const option = document.createElement('option');
                 option.value = student.id;
                 option.textContent = student.name;
                 studentDropdown.appendChild(option);
             });
             studentDropdown.disabled = studentsToDisplay.length === 0 && classId !== null && classId !== undefined && classId !== ''; // Disable if no students in selected class, unless showing all
             studentDropdown.value = selectedValue || currentSelected;
             if (studentDropdown.value && studentDropdown.value != currentSelected) {
                  studentDropdown.dispatchEvent(new Event('change'));
             }
             if (!studentDropdown.value && selectedValue) {
                 // If we tried to set a value but it wasn't in the list (e.g., student deleted or class changed)
                 console.warn(`Could not set student dropdown (${dropdownId}) value to ${selectedValue}. Value not found in list.`);
             }
         }

         function populateUserDropdowns(selectedValue = '') {
             const userDropdowns = document.querySelectorAll('select[id$="-user"]');
              userDropdowns.forEach(dropdown => {
                 const currentSelected = dropdown.value;
                 dropdown.innerHTML = '<option value="">-- Select User --</option>';
                  cache.users.forEach(user => {
                     const option = document.createElement('option');
                     option.value = user.id;
                     option.textContent = user.name + ` (${user.type})`;
                     dropdown.appendChild(option);
                 });
                 dropdown.value = selectedValue || currentSelected;
                 if (dropdown.value && dropdown.value != currentSelected) {
                      dropdown.dispatchEvent(new Event('change'));
                  }
              });
         }

        function populateFeeDropdowns(selectedValue = '') {
             const feeDropdowns = document.querySelectorAll('select[id$="-fee"]');
              feeDropdowns.forEach(dropdown => {
                 const currentSelected = dropdown.value;
                 dropdown.innerHTML = '<option value="">-- Select Fee Type --</option>';
                  cache.fees.forEach(fee => {
                     const option = document.createElement('option');
                     option.value = fee.id;
                      const assignedClass = fee.assignedClassId ? ` (${cache.classes.find(c => c.id == fee.assignedClassId)?.name || 'Unknown Class'})` : '';
                     option.textContent = `${fee.name} (${fee.amount.toFixed(2)})${assignedClass}`;
                     dropdown.appendChild(option);
                 });
                 dropdown.value = selectedValue || currentSelected;
                 if (dropdown.value && dropdown.value != currentSelected) {
                      dropdown.dispatchEvent(new Event('change'));
                  }
              });
         }


         function renderGradesTable(filteredGrades = cache.grades) {
            const gradesTableBody = document.getElementById('grades-table').querySelector('tbody');
            const noGradesMessage = document.getElementById('no-grades-message');
            gradesTableBody.innerHTML = ''; // Clear table body

            if (!filteredGrades || filteredGrades.length === 0) {
                 noGradesMessage.classList.remove('hidden');
                 return;
            } else {
                 noGradesMessage.classList.add('hidden');
            }

            filteredGrades.forEach(grade => {
                const row = document.createElement('tr');

                // Find linked data from cache
                const student = cache.students.find(s => s.id == grade.studentId);
                const studentName = student ? student.name : 'Unknown Student';
                const className = student ? (cache.classes.find(c => c.id == student.classId)?.name || 'Unknown Class') : 'Unknown Class';
                const subjectName = cache.subjects.find(sub => sub.id == grade.subjectId)?.name || 'Unknown Subject';


                row.innerHTML = `
                    <td>${escapeHTML(className)}</td>
                    <td>${escapeHTML(studentName)}</td>
                    <td>${escapeHTML(subjectName)}</td>
                    <td>${escapeHTML(grade.period)}</td>
                    <td>${escapeHTML(String(grade.grade))}</td>
                    <td>${escapeHTML(grade.comment || '')}</td>
                    <td>
                         <button class="edit" data-id="${grade.id}">Edit</button>
                         <button class="delete" data-id="${grade.id}">Delete</button>
                    </td>
                `;
                gradesTableBody.appendChild(row);
            });
         }

         async function renderFeesList() {
            const feesList = document.getElementById('fees-list');
            feesList.innerHTML = '';
             if (cache.fees.length === 0) {
                 feesList.innerHTML = '<li>No fee types defined yet.</li>';
                 return;
            }
            cache.fees.forEach(fee => {
                 const assignedClass = fee.assignedClassId ? ` (${cache.classes.find(c => c.id == fee.assignedClassId)?.name || 'Unknown Class'})` : ' (All Classes)';
                 const li = document.createElement('li');
                 li.innerHTML = `
                     <span class="list-item-info">${escapeHTML(fee.name)}: $${fee.amount.toFixed(2)}${escapeHTML(assignedClass)}</span>
                     <span class="list-item-actions">
                         <button class="edit" data-id="${fee.id}">Edit</button>
                         <button class="delete" data-id="${fee.id}">Delete</button>
                     </span>
                 `;
                 feesList.appendChild(li);
             });
         }

         async function renderPaymentsList() {
            const paymentsList = document.getElementById('payments-list');
            paymentsList.innerHTML = '';
             if (cache.payments.length === 0) {
                 paymentsList.innerHTML = '<li>No payment records yet.</li>';
                 return;
            }
            cache.payments.forEach(payment => {
                 const student = cache.students.find(s => s.id == payment.studentId);
                 const studentName = student ? student.name : 'Unknown Student';
                 const fee = cache.fees.find(f => f.id == payment.feeId);
                 const feeName = fee ? fee.name : 'Unknown Fee';
                 const date = payment.paymentDate ? new Date(payment.paymentDate).toLocaleDateString() : 'Unknown Date';

                 const li = document.createElement('li');
                  li.innerHTML = `
                     <span class="list-item-info">${escapeHTML(studentName)} paid $${parseFloat(payment.amount).toFixed(2)} for ${escapeHTML(feeName)} on ${escapeHTML(date)}</span>
                     <span class="list-item-actions">
                         <button class="edit" data-id="${payment.id}">Edit</button>
                         <button class="delete" data-id="${payment.id}">Delete</button>
                     </span>
                 `;
                 paymentsList.appendChild(li);
             });
         }

         async function renderUsersList() {
            const usersList = document.getElementById('users-list');
            usersList.innerHTML = '';
             if (cache.users.length === 0) {
                 usersList.innerHTML = '<li>No users added yet.</li>';
                 return;
            }
            cache.users.forEach(user => {
                 const li = document.createElement('li');
                 li.innerHTML = `
                     <span class="list-item-info">${escapeHTML(user.name)} (${escapeHTML(user.type)})</span>
                     <span class="list-item-actions">
                         <button class="edit" data-id="${user.id}">Edit</button>
                         <button class="delete" data-id="${user.id}">Delete</button>
                     </span>
                 `;
                 usersList.appendChild(li);
             });
         }


        // Basic HTML escaping to prevent XSS if user input contains HTML
        function escapeHTML(str) {
             if (typeof str === 'string') {
                 const div = document.createElement('div');
                 div.appendChild(document.createTextNode(str));
                 return div.innerHTML;
             }
             return str; // Return non-strings as is
        }

        // --- Form Management Helpers ---

        function setFormForEdit(formId, itemData) {
            const form = document.getElementById(formId);
            const idField = form.querySelector('input[type="hidden"]');
            if (idField) idField.value = itemData.id;

            // Populate fields based on itemData keys (assuming field IDs match keys)
            for (const key in itemData) {
                // Construct potential ID based on formId prefix and key
                const fieldId = `${formId.split('-')[0]}-${key}`; // e.g., 'class-name' from 'class-form' and 'name'
                const field = form.querySelector(`#${fieldId}`);

                if (field) {
                     if (field.type === 'checkbox') {
                         field.checked = itemData[key];
                     } else if (field.tagName === 'SELECT') {
                         // Handle dropdowns separately if needed (e.g., populate then set value)
                         // Ensure dropdown is populated before setting value
                          // This is tricky with async population.
                          // A better approach is to ensure dropdowns are populated FIRST when showing section,
                          // then rely on the default logic below.
                         field.value = itemData[key];
                         // Trigger change if value is set - important for dependent dropdowns (like student after class)
                         // Use setTimeout to ensure event fires AFTER potential sync population if needed
                         setTimeout(() => {
                            field.dispatchEvent(new Event('change'));
                         }, 0);

                     } else {
                         field.value = itemData[key];
                     }
                } else {
                    // console.warn(`Field with ID '${fieldId}' not found for key '${key}' in form '${formId}'.`);
                }
            }

            // Toggle buttons - relies on convention like 'add-class-btn', 'update-class-btn', 'cancel-class-edit'
            const prefix = formId.split('-')[0];
             form.querySelector(`#add-${prefix}-btn`)?.classList.add('hidden');
             form.querySelector(`#update-${prefix}-btn`)?.classList.remove('hidden');
             form.querySelector(`#cancel-${prefix}-edit`)?.classList.remove('hidden');

             // Specific handling for grade form selects during edit
             if (formId === 'grade-form') {
                 document.getElementById('grade-class').disabled = true; // Prevent changing class/student during grade edit
                 document.getElementById('grade-student').disabled = true;
             }
        }

        function resetForm(formId) {
             const form = document.getElementById(formId);
             form.reset(); // Reset all fields

             const idField = form.querySelector('input[type="hidden"]');
             if(idField) idField.value = ''; // Clear hidden ID

             // Toggle buttons back to add state
             const prefix = formId.split('-')[0];
             form.querySelector(`#add-${prefix}-btn`)?.classList.remove('hidden');
             form.querySelector(`#update-${prefix}-btn`)?.classList.add('hidden');
             form.querySelector(`#cancel-${prefix}-edit`)?.classList.add('hidden');

             // Re-enable specific grade form selects after canceling edit
             if (formId === 'grade-form') {
                 document.getElementById('grade-class').disabled = false;
                 // Student dropdown will be disabled until a class is selected
                 document.getElementById('grade-student').disabled = true;
                 // Clear and re-populate student dropdown based on cleared class value
                 populateStudentDropdown('grade-student', '');
             }
        }


        // --- Event Handlers ---

        async function handleFormSubmit(event, storeName, onSuccess) {
            event.preventDefault(); // Ensure form doesn't submit and reload the page
            showStatusMessage('Saving...', 'info', 0); // Show persistent saving message

            const form = event.target;
            const idField = form.querySelector('input[type="hidden"]');
            // Use == for potential type mismatch between form value (string) and DB key (number)
            const id = idField ? (idField.value ? (storeName === STORES.SCHOOL_INFO ? idField.value : parseInt(idField.value)) : null) : null; // Parse ID as number unless school info, or null if new

            const formData = new FormData(form);
            const data = {};
            for (const [key, value] of formData.entries()) {
                // Basic type conversion based on field type/name convention if needed
                 if (form.querySelector(`#${form.id.split('-')[0]}-${key}`)?.type === 'number') {
                     data[key] = parseFloat(value) || 0; // Convert numbers
                 } else {
                    data[key] = value;
                 }
            }

            try {
                 let operation;
                 if (id !== null) {
                    // Update existing item
                     data.id = id; // Add ID back to data object
                     await updateData(storeName, data);
                     operation = 'updated';
                } else {
                    // Add new item (ID is auto-incremented by IndexedDB unless school info)
                     if (storeName === STORES.SCHOOL_INFO) {
                         // School info is special - it's a singleton (key 'singleton')
                        await updateData(storeName, { id: 'singleton', ...data }); // Use put to add or update the single item
                     } else {
                        await addData(storeName, data); // Use add for auto-incrementing items
                     }
                     operation = 'added';
                }

                showStatusMessage(`${storeName.slice(0, -1)} ${operation} successfully!`, 'success');

                 // Refresh relevant cache and re-render UI after successful operation
                 await refreshCache(storeName);

                // If the updated/added item affects dropdowns or lists in other sections, refresh those caches/UI too
                 // Use Promises to handle async updates if needed
                 const updatePromises = [];
                 if (storeName === STORES.CLASSES) {
                     updatePromises.push(populateClassDropdowns());
                     updatePromises.push(refreshCache(STORES.STUDENTS).then(() => renderStudents())); // Students list depends on class names
                     updatePromises.push(refreshCache(STORES.GRADES).then(() => renderGradesTable(applyFilters()))); // Grades table depends on class/student/subject names
                     updatePromises.push(refreshCache(STORES.FEES).then(() => populateFeeDropdowns())); // Fees might be assigned to classes
                 } else if (storeName === STORES.SUBJECTS) {
                      updatePromises.push(populateSubjectDropdowns());
                     updatePromises.push(refreshCache(STORES.GRADES).then(() => renderGradesTable(applyFilters()))); // Grades table depends on subject names
                 } else if (storeName === STORES.STUDENTS) {
                     // Update student dropdowns everywhere
                     updatePromises.push(populateStudentDropdown('grade-student', document.getElementById('grade-class').value));
                     updatePromises.push(populateStudentDropdown('filter-student', document.getElementById('filter-class').value));
                     updatePromises.push(populateStudentDropdown('payment-student', document.getElementById('payment-student').value)); // Fees payment
                     updatePromises.push(refreshCache(STORES.GRADES).then(() => renderGradesTable(applyFilters()))); // Grades table depends on student names
                      renderStudents(); // Re-render student list explicitly
                 } else if (storeName === STORES.GRADES) {
                      // Re-render the grades table based on current filters
                      renderGradesTable(applyFilters());
                 } else if (storeName === STORES.FEES) {
                      updatePromises.push(populateFeeDropdowns());
                      renderFeesList(); // Re-render fees list
                       updatePromises.push(refreshCache(STORES.PAYMENTS).then(() => renderPaymentsList())); // Payments list depends on fee names
                 } else if (storeName === STORES.PAYMENTS) {
                     renderPaymentsList(); // Re-render payments list
                     // Note: Balance calculations/reports are NOT implemented here
                 } else if (storeName === STORES.USERS) {
                     renderUsersList(); // Re-render users list
                      updatePromises.push(populateUserDropdowns()); // If any dropdown uses users
                 }

                await Promise.all(updatePromises); // Wait for all related UI updates

            } catch (error) {
                  console.error(`Error saving data to ${storeName}:`, error);
                  showStatusMessage(`Failed to save data to ${storeName.slice(0, -1)}: ${error.message}`, 'error', 5000);
                 return; // Stop if save fails
            }


            resetForm(form.id); // Reset form fields and buttons
            if (onSuccess) onSuccess(); // Execute any specific success logic
        }

         async function handleDeleteItem(storeName, id) {
             if (!confirm(`Are you sure you want to delete this ${storeName.slice(0, -1)}? This cannot be undone and related data might be affected.`)) {
                 return;
             }
             showStatusMessage('Deleting...', 'info', 0);
             try {
                 await deleteData(storeName, id);
                 showStatusMessage(`${storeName.slice(0, -1)} deleted successfully!`, 'success');

                 // Refresh relevant cache and re-render UI
                 await refreshCache(storeName);

                 // If deletion affects dropdowns/lists/tables in other sections, refresh those
                 const updatePromises = [];
                 if (storeName === STORES.CLASSES) {
                      // Filtering out dependent items *after* cache refresh is simpler
                      cache.students = cache.students.filter(s => s.classId != id);
                       // Find grades for students who were in this class
                       const studentsInDeletedClass = cache.students.filter(s => s.classId != id); // Filter students NOT in the deleted class
                       const remainingStudentIds = new Set(studentsInDeletedClass.map(s => s.id));
                       cache.grades = cache.grades.filter(g => remainingStudentIds.has(g.studentId));
                       cache.fees = cache.fees.filter(f => f.assignedClassId != id); // Remove fees assigned *only* to this class

                       updatePromises.push(populateClassDropdowns());
                       renderStudents();
                       renderGradesTable(applyFilters());
                       populateFeeDropdowns();
                       updatePromises.push(refreshCache(STORES.PAYMENTS).then(() => renderPaymentsList())); // Payments might be linked to students now removed
                 } else if (storeName === STORES.SUBJECTS) {
                      cache.grades = cache.grades.filter(g => g.subjectId != id); // Remove grades for this subject
                      updatePromises.push(populateSubjectDropdowns());
                      renderGradesTable(applyFilters());
                 } else if (storeName === STORES.STUDENTS) {
                      cache.grades = cache.grades.filter(g => g.studentId != id); // Remove grades for this student
                       cache.payments = cache.payments.filter(p => p.studentId != id); // Remove payments for this student

                     updatePromises.push(populateStudentDropdown('grade-student', document.getElementById('grade-class').value));
                     updatePromises.push(populateStudentDropdown('filter-student', document.getElementById('filter-class').value));
                     updatePromises.push(populateStudentDropdown('payment-student', document.getElementById('payment-student').value));
                      renderStudents();
                      renderGradesTable(applyFilters());
                      renderPaymentsList();
                 } else if (storeName === STORES.GRADES) {
                      renderGradesTable(applyFilters()); // Just re-render the filtered view
                 } else if (storeName === STORES.FEES) {
                      cache.payments = cache.payments.filter(p => p.feeId != id); // Remove payments for this fee type
                      renderFeesList();
                      populateFeeDropdowns();
                      renderPaymentsList();
                 } else if (storeName === STORES.PAYMENTS) {
                     renderPaymentsList();
                 } else if (storeName === STORES.USERS) {
                     renderUsersList();
                     populateUserDropdowns();
                     // Note: Deleting a user does NOT automatically delete linked students/parents etc.
                     // Full system would handle cascading deletes or prevent deletion if linked.
                 }
                 await Promise.all(updatePromises);


             } catch (error) {
                  console.error(`Error deleting data from ${storeName}:`, error);
                  showStatusMessage(`Failed to delete ${storeName.slice(0, -1)}: ${error.message}`, 'error', 5000);
                  // Re-render lists just in case the cache refresh failed partially
                  if (storeName === STORES.CLASSES) renderClasses();
                  else if (storeName === STORES.SUBJECTS) renderSubjects();
                  else if (storeName === STORES.STUDENTS) renderStudents();
                  else if (storeName === STORES.GRADES) renderGradesTable(applyFilters());
                  else if (storeName === STORES.FEES) renderFeesList();
                  else if (storeName === STORES.PAYMENTS) renderPaymentsList();
                   else if (storeName === STORES.USERS) renderUsersList();
             }
         }

        // Helper to get filter values and apply them to grades
         function applyFilters() {
            const filterClassId = document.getElementById('filter-class').value;
            const filterStudentId = document.getElementById('filter-student').value;
            const filterSubjectId = document.getElementById('filter-subject').value;
            const filterPeriod = document.getElementById('filter-period').value;

            let filtered = cache.grades;

            // Filter by class (need to find students in the class first)
            if (filterClassId) {
                const studentIdsInClass = cache.students.filter(s => s.classId == filterClassId).map(s => s.id);
                filtered = filtered.filter(grade => studentIdsInClass.includes(grade.studentId));
            }

            // Filter by student (if a specific student is selected)
             if (filterStudentId) {
                filtered = filtered.filter(grade => grade.studentId == filterStudentId);
            }

            // Filter by subject
            if (filterSubjectId) {
                filtered = filtered.filter(grade => grade.subjectId == filterSubjectId);
            }

            // Filter by period
            if (filterPeriod) {
                filtered = filtered.filter(grade => grade.period === filterPeriod);
            }
            return filtered;
         }


        // --- Report Placeholder Logic ---
        function generateReportCardPlaceholder(studentId) {
            const student = cache.students.find(s => s.id == studentId);
             if (!student) return 'Student not found.';
            const studentGrades = cache.grades.filter(g => g.studentId == studentId);
            const studentClass = cache.classes.find(c => c.id == student.classId)?.name || 'Unassigned';

            let output = `--- Report Card (Placeholder) ---\n\n`;
            output += `Student: ${student.name}\n`;
            output += `Class: ${studentClass}\n\n`;
            output += `Grades:\n`;

            if (studentGrades.length === 0) {
                output += "No grades recorded for this student.\n";
            } else {
                 // Group grades by subject
                 const gradesBySubject = studentGrades.reduce((acc, grade) => {
                     const subjectName = cache.subjects.find(s => s.id == grade.subjectId)?.name || `Subject ${grade.subjectId}`;
                     if (!acc[subjectName]) {
                         acc[subjectName] = [];
                     }
                     acc[subjectName].push(grade);
                     return acc;
                 }, {});

                 for (const subjectName in gradesBySubject) {
                     output += `  ${subjectName}:\n`;
                     gradesBySubject[subjectName].forEach(grade => {
                         output += `    - ${grade.period}: ${grade.grade}${grade.comment ? ` (${grade.comment})` : ''}\n`;
                     });
                 }
            }

             output += `\n--- End Report (Placeholder) ---`;
             return output;
        }

         function generateGradeSheetPlaceholder(classId) {
             const cls = cache.classes.find(c => c.id == classId);
             if (!cls) return 'Class not found.';

             const studentsInClass = cache.students.filter(s => s.classId == classId);
             const classGrades = cache.grades.filter(g => studentsInClass.map(s => s.id).includes(g.studentId));
             const subjectsInClass = [...new Set(classGrades.map(g => g.subjectId))].map(id => cache.subjects.find(s => s.id == id)?.name || `Subject ${id}`);
             const periods = [...new Set(classGrades.map(g => g.period))];

             let output = `--- Grade Sheet (Placeholder) ---\n\n`;
             output += `Class: ${cls.name}\n\n`;

             if (studentsInClass.length === 0) {
                  output += "No students in this class.\n";
             } else if (classGrades.length === 0) {
                 output += "No grades recorded for this class.\n";
             } else {
                 // Very basic table-like structure (text based)
                 output += `Student | ${subjectsInClass.join(' | ')}\n`;
                 output += `--------|-${subjectsInClass.map(() => '-------').join('-|-')}\n`; // Separator line

                 studentsInClass.forEach(student => {
                     let studentRow = `${student.name} |`;
                     subjectsInClass.forEach(subjectName => {
                         const subjectId = cache.subjects.find(s => s.name === subjectName)?.id;
                         const studentSubjectGrades = classGrades.filter(g => g.studentId == student.id && g.subjectId == subjectId);

                         if (studentSubjectGrades.length > 0) {
                             // Show some representation - e.g., average or just periods
                             const gradesText = periods.map(p => {
                                  const grade = studentSubjectGrades.find(g => g.period === p);
                                  return grade ? `${p}:${grade.grade}` : '';
                             }).filter(Boolean).join(', ');
                             studentRow += ` ${gradesText} |`;
                         } else {
                             studentRow += ` N/A |`;
                         }
                     });
                     output += studentRow + "\n";
                 });
             }

             output += `\n--- End Grade Sheet (Placeholder) ---`;
             return output;
         }

        function generateStudentProgressPlaceholder(studentId) {
             const student = cache.students.find(s => s.id == studentId);
             if (!student) return 'Student not found.';
             const studentGrades = cache.grades.filter(g => g.studentId == studentId);

             let output = `--- Student Progress (Placeholder) ---\n\n`;
             output += `Student: ${student.name}\n`;
             output += `Class: ${cache.classes.find(c => c.id == student.classId)?.name || 'Unassigned'}\n\n`;

              if (studentGrades.length === 0) {
                output += "No grades recorded for this student.\n";
            } else {
                 output += "All Recorded Grades:\n";
                 studentGrades.forEach(grade => {
                      const subjectName = cache.subjects.find(s => s.id == grade.subjectId)?.name || `Subject ${grade.subjectId}`;
                      output += `- ${subjectName}, ${grade.period}: ${grade.grade}${grade.comment ? ` (${grade.comment})` : ''}\n`;
                 });
                 // More sophisticated progress would show trends, comparisons etc.
             }
             output += `\n--- End Progress (Placeholder) ---`;
             return output;
        }

         function generateClassPerformancePlaceholder(classId) {
             const cls = cache.classes.find(c => c.id == classId);
             if (!cls) return 'Class not found.';

             const studentsInClass = cache.students.filter(s => s.classId == classId);
             const classGrades = cache.grades.filter(g => studentsInClass.map(s => s.id).includes(g.studentId));
             const subjectsInClass = [...new Set(classGrades.map(g => g.subjectId))].map(id => cache.subjects.find(s => s.id == id)?.name || `Subject ${id}`);
             const periods = [...new Set(classGrades.map(g => g.period))];


             let output = `--- Class Performance (Placeholder) ---\n\n`;
             output += `Class: ${cls.name}\n\n`;

             if (studentsInClass.length === 0) {
                  output += "No students in this class.\n";
             } else if (classGrades.length === 0) {
                 output += "No grades recorded for this class.\n";
             } else {
                 output += "Subject Performance Summary (Average Grade by Period):\n";
                  subjectsInClass.forEach(subjectName => {
                       const subjectId = cache.subjects.find(s => s.name === subjectName)?.id;
                       output += `  ${subjectName}:\n`;
                        periods.forEach(period => {
                            const gradesForPeriod = classGrades.filter(g => g.subjectId == subjectId && g.period === period);
                             const average = gradesForPeriod.length > 0
                                 ? (gradesForPeriod.reduce((sum, g) => sum + g.grade, 0) / gradesForPeriod.length).toFixed(2)
                                 : 'N/A';
                            output += `    - ${period}: ${average}\n`;
                        });
                  });

                  output += "\nOverall Class Average (across all subjects/periods with grades):\n";
                  const allGradesInClass = classGrades.map(g => g.grade);
                  const overallAverage = allGradesInClass.length > 0
                      ? (allGradesInClass.reduce((sum, grade) => sum + grade, 0) / allGradesInClass.length).toFixed(2)
                      : 'N/A';
                  output += `  Overall Average: ${overallAverage}\n`;
             }


             output += `\n--- End Class Performance (Placeholder) ---`;
             return output;
         }


        // --- Initialization ---
        document.addEventListener('DOMContentLoaded', async () => {
            showStatusMessage('Initializing database...', 'info', 0);
            try {
                 await openDatabase();
                 showStatusMessage('Database ready.', 'success');

                 await refreshAllCaches();

                // Render initial sections and populate dropdowns after caches are loaded
                 renderSchoolInfo();
                 renderClasses();
                 renderSubjects();
                 renderStudents();
                 renderGradesTable(); // Show all grades initially
                 renderFeesList(); // Initial render for fees
                 renderPaymentsList(); // Initial render for payments
                 renderUsersList(); // Initial render for users

                 // Populate dropdowns used across different forms/sections
                 populateClassDropdowns();
                 populateSubjectDropdowns();
                 populateStudentDropdown('grade-student', ''); // Grade entry student (disabled by default until class)
                 populateStudentDropdown('filter-student', ''); // View grades filter student (shows all)
                 populateStudentDropdown('report-student', ''); // Reports student (shows all)
                 populateStudentDropdown('payment-student', ''); // Fees payment student (shows all)
                 populateFeeDropdowns(); // Fees payment fee dropdown
                 populateUserDropdowns(); // Users dropdowns if any (none implemented yet)


                // Add event listeners for sidebar navigation
                document.querySelectorAll('.sidebar li').forEach(item => {
                    item.addEventListener('click', function() {
                        const sectionId = this.getAttribute('data-section');
                        if (sectionId) {
                            showSection(sectionId);
                        }
                    });
                });

                // --- Add Specific Form Event Listeners ---

                // School Info Form
                document.getElementById('school-info-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.SCHOOL_INFO);
                });

                // Class Form (delegated handlers for edit/delete buttons on list)
                document.getElementById('class-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.CLASSES);
                });
                 document.getElementById('classes-list').addEventListener('click', async (e) => {
                     const target = e.target;
                     // Use == for potential type mismatch with data-id attributes
                     const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                     if (target.classList.contains('edit') && id !== null) {
                         const item = await getDataById(STORES.CLASSES, id);
                         if (item) setFormForEdit('class-form', item);
                     } else if (target.classList.contains('delete') && id !== null) {
                          await handleDeleteItem(STORES.CLASSES, id);
                     }
                 });
                 document.getElementById('cancel-class-edit').addEventListener('click', () => resetForm('class-form'));


                // Subject Form (delegated handlers for edit/delete buttons on list)
                document.getElementById('subject-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.SUBJECTS);
                });
                 document.getElementById('subjects-list').addEventListener('click', async (e) => {
                     const target = e.target;
                     const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                      if (target.classList.contains('edit') && id !== null) {
                         const item = await getDataById(STORES.SUBJECTS, id);
                         if (item) setFormForEdit('subject-form', item);
                      } else if (target.classList.contains('delete') && id !== null) {
                           await handleDeleteItem(STORES.SUBJECTS, id);
                      }
                 });
                 document.getElementById('cancel-subject-edit').addEventListener('click', () => resetForm('subject-form'));

                // Student Form (delegated handlers for edit/delete buttons on list)
                document.getElementById('student-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.STUDENTS);
                });
                 document.getElementById('students-list').addEventListener('click', async (e) => {
                     const target = e.target;
                     const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                      if (target.classList.contains('edit') && id !== null) {
                         const item = await getDataById(STORES.STUDENTS, id);
                         if (item) setFormForEdit('student-form', item);
                      } else if (target.classList.contains('delete') && id !== null) {
                           await handleDeleteItem(STORES.STUDENTS, id);
                      }
                 });
                 document.getElementById('cancel-student-edit').addEventListener('click', () => resetForm('student-form'));


                // Grade Form
                document.getElementById('grade-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.GRADES);
                });

                 // Event listener to update student dropdown in Grade Entry when class changes
                 document.getElementById('grade-class').addEventListener('change', (event) => {
                     const selectedClassId = event.target.value ? parseInt(event.target.value) : null;
                     // When class changes, reset student dropdown and try to populate
                     populateStudentDropdown('grade-student', selectedClassId);
                 });
                document.getElementById('cancel-grade-edit').addEventListener('click', () => resetForm('grade-form'));


                // View Grades Filter Form
                 document.getElementById('filter-grades-form').addEventListener('submit', function(event) {
                    event.preventDefault();
                    const filtered = applyFilters();
                    renderGradesTable(filtered);
                 });

                 document.getElementById('reset-filter').addEventListener('click', function() {
                     document.getElementById('filter-class').value = '';
                     // Manually re-populate filter student dropdown with all students after clearing class filter
                     populateStudentDropdown('filter-student', ''); // Passing null/empty string shows all
                     document.getElementById('filter-subject').value = '';
                     document.getElementById('filter-period').value = '';
                     renderGradesTable(cache.grades); // Render all grades from cache
                 });

                  // Event listener to update filter student dropdown in View Grades when filter class changes
                 document.getElementById('filter-class').addEventListener('change', (event) => {
                     const selectedClassId = event.target.value ? parseInt(event.target.value) : null;
                     populateStudentDropdown('filter-student', selectedClassId); // Filter student dropdown based on selected class
                 });

                 // Delegated handlers for edit/delete buttons on Grades table
                 document.getElementById('grades-table').addEventListener('click', async (e) => {
                     const target = e.target;
                     const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                     if (target.classList.contains('edit') && id !== null) {
                          const grade = await getDataById(STORES.GRADES, id);
                          if (grade) {
                             showSection('grade-entry'); // Navigate to grade entry section
                             // Populate grade form - ensure dropdowns are populated first
                             populateGradeEntryDropdowns();

                              // Set the form fields after a slight delay to allow dropdowns to populate
                              setTimeout(() => {
                                  // Need to find student's class to set the grade-class dropdown
                                   const student = cache.students.find(s => s.id == grade.studentId);
                                   if(student) {
                                       document.getElementById('grade-class').value = student.classId;
                                       // Trigger change on class dropdown to populate student dropdown
                                        document.getElementById('grade-class').dispatchEvent(new Event('change'));

                                        // Set other fields after student dropdown populates (another small delay)
                                        setTimeout(() => {
                                            document.getElementById('grade-student').value = grade.studentId;
                                            document.getElementById('grade-subject').value = grade.subjectId;
                                            document.getElementById('grade-period').value = grade.period;
                                            document.getElementById('student-grade').value = grade.grade;
                                            document.getElementById('student-comment').value = grade.comment;

                                             // Disable class/student selects during edit
                                            document.getElementById('grade-class').disabled = true;
                                             document.getElementById('grade-student').disabled = true;

                                            // Set form to edit mode
                                            setFormForEdit('grade-form', grade);

                                        }, 50); // Adjust delay if needed
                                   } else {
                                        showStatusMessage('Could not load student data for editing grade.', 'error');
                                        resetForm('grade-form');
                                   }
                              }, 0); // Initial delay

                         }
                     } else if (target.classList.contains('delete') && id !== null) {
                           await handleDeleteItem(STORES.GRADES, id);
                     }
                 });


                // --- Reports Placeholder Event Listeners ---
                 document.getElementById('report-options-form').addEventListener('change', (event) => {
                     // Optional: Add logic here to clear previous report output when options change
                      if (event.target.id === 'report-class' || event.target.id === 'report-student') {
                          document.getElementById('report-output').textContent = 'Select report options and click a button to see placeholder output.';
                      }
                 });

                 document.getElementById('generate-reportcard').addEventListener('click', () => {
                     const studentId = document.getElementById('report-student').value;
                     if (!studentId) {
                          document.getElementById('report-output').textContent = 'Please select a student for the report card.';
                         return;
                     }
                     const report = generateReportCardPlaceholder(parseInt(studentId));
                     document.getElementById('report-output').textContent = report;
                 });

                document.getElementById('generate-gradesheet').addEventListener('click', () => {
                     const classId = document.getElementById('report-class').value;
                     if (!classId) {
                          document.getElementById('report-output').textContent = 'Please select a class for the grade sheet.';
                         return;
                     }
                     const report = generateGradeSheetPlaceholder(parseInt(classId));
                     document.getElementById('report-output').textContent = report;
                });

                document.getElementById('generate-studentprogress').addEventListener('click', () => {
                     const studentId = document.getElementById('report-student').value;
                     if (!studentId) {
                          document.getElementById('report-output').textContent = 'Please select a student for the progress report.';
                         return;
                     }
                     const report = generateStudentProgressPlaceholder(parseInt(studentId));
                     document.getElementById('report-output').textContent = report;
                });

                 document.getElementById('generate-classperformance').addEventListener('click', () => {
                     const classId = document.getElementById('report-class').value;
                     if (!classId) {
                          document.getElementById('report-output').textContent = 'Please select a class for the class performance report.';
                         return;
                     }
                     const report = generateClassPerformancePlaceholder(parseInt(classId));
                     document.getElementById('report-output').textContent = report;
                 });


                // --- Fees Placeholder Event Listeners ---
                document.getElementById('fee-setup-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.FEES);
                });

                document.getElementById('fees-list').addEventListener('click', async (e) => {
                    const target = e.target;
                    const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                    if (target.classList.contains('edit') && id !== null) {
                        const item = await getDataById(STORES.FEES, id);
                        if (item) setFormForEdit('fee-setup-form', item);
                    } else if (target.classList.contains('delete') && id !== null) {
                        await handleDeleteItem(STORES.FEES, id);
                    }
                });

                document.getElementById('cancel-fee-edit').addEventListener('click', () => resetForm('fee-setup-form'));

                document.getElementById('fee-payment-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.PAYMENTS);
                });

                document.getElementById('payments-list').addEventListener('click', async (e) => {
                    const target = e.target;
                    const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                    if (target.classList.contains('edit') && id !== null) {
                        const item = await getDataById(STORES.PAYMENTS, id);
                        if (item) setFormForEdit('fee-payment-form', item);
                    } else if (target.classList.contains('delete') && id !== null) {
                        await handleDeleteItem(STORES.PAYMENTS, id);
                    }
                });
                  document.getElementById('cancel-payment-edit').addEventListener('click', () => resetForm('fee-payment-form'));


                // --- Users Placeholder Event Listeners ---
                document.getElementById('user-form').addEventListener('submit', function(e) {
                    e.preventDefault(); // Prevent default form submission
                    handleFormSubmit(e, STORES.USERS);
                });

                document.getElementById('users-list').addEventListener('click', async (e) => {
                    const target = e.target;
                    const id = target.dataset.id ? parseInt(target.dataset.id) : null;
                    if (target.classList.contains('edit') && id !== null) {
                        const item = await getDataById(STORES.USERS, id);
                        if (item) setFormForEdit('user-form', item);
                    } else if (target.classList.contains('delete') && id !== null) {
                        await handleDeleteItem(STORES.USERS, id);
                    }
                });

                document.getElementById('cancel-user-edit').addEventListener('click', () => resetForm('user-form'));


                // --- Populate Dropdowns Initially (after caches are loaded and DB open) ---
                 function populateGradeEntryDropdowns() {
                     populateClassDropdowns(document.getElementById('grade-class').value);
                     populateSubjectDropdowns(document.getElementById('grade-subject').value);
                      // Student dropdown populated by 'change' event on class select
                 }

                 function populateViewGradesDropdowns() {
                     populateClassDropdowns(document.getElementById('filter-class').value);
                      // Student dropdown populated by 'change' event on class select
                      populateSubjectDropdowns(document.getElementById('filter-subject').value);
                 }

                  function populateReportDropdowns() {
                      populateClassDropdowns(document.getElementById('report-class').value);
                      populateStudentDropdown('report-student', null, document.getElementById('report-student').value); // Report student dropdown shows ALL students initially
                       // Event listener for Report Class select to filter Report Student select
                      document.getElementById('report-class').addEventListener('change', (event) => {
                           const selectedClassId = event.target.value ? parseInt(event.target.value) : null;
                           populateStudentDropdown('report-student', selectedClassId);
                       });
                  }

                 function populateFeesDropdowns() {
                     // Populate class dropdown in Fee Setup
                     populateClassDropdowns(document.getElementById('fee-assigned-class').value);
                      // Populate student dropdown in Fee Payment (show all students)
                     populateStudentDropdown('payment-student', null, document.getElementById('payment-student').value);
                     // Populate fee dropdown in Fee Payment
                     populateFeeDropdowns(document.getElementById('payment-fee').value);
                 }


                // Initial dropdown populations that need caches ready
                 populateGradeEntryDropdowns(); // Grade Entry
                 populateViewGradesDropdowns(); // View Grades
                 populateReportDropdowns(); // Reports
                 populateFeesDropdowns(); // Fees
                 // Users dropdowns would go here if implemented

            } catch (error) {
                 console.error('Initialization failed:', error);
                 showStatusMessage('System failed to initialize. Please check console for details.', 'error', 0);
            }

        });

        // --- Navigation Logic ---
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.style.display = 'none';
            });

            // Show the target section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
            }

            // Update active class in sidebar
            document.querySelectorAll('.sidebar li').forEach(item => {
                 item.classList.remove('active');
            });
             const activeMenuItem = document.querySelector(`.sidebar li[data-section="${sectionId}"]`);
             if(activeMenuItem) {
                 activeMenuItem.classList.add('active');
             }

             // Perform rendering/population specific to the section being shown
             // Note: Some populations happen on DOMContentLoaded and within CRUD handlers
             // This focuses on ensuring UI reflects latest cache when section is explicitly navigated to.
             if (sectionId === 'school-info') {
                 renderSchoolInfo();
             } else if (sectionId === 'classes') {
                 renderClasses();
                 resetForm('class-form');
             } else if (sectionId === 'subjects') {
                  renderSubjects();
                  resetForm('subject-form');
             } else if (sectionId === 'students') {
                  populateClassDropdowns(); // Ensure class dropdown is fresh for add/edit form
                  renderStudents();
                  resetForm('student-form');
             } else if (sectionId === 'grade-entry') {
                 populateGradeEntryDropdowns(); // Ensure all dropdowns are fresh
                 resetForm('grade-form');
             } else if (sectionId === 'view-grades') {
                 populateViewGradesDropdowns(); // Ensure filter dropdowns are fresh
                 renderGradesTable(applyFilters()); // Re-render table with current filters
             } else if (sectionId === 'reports') {
                 populateReportDropdowns(); // Ensure report option dropdowns are fresh
                 document.getElementById('report-output').textContent = 'Select report options and click a button to see placeholder output.'; // Reset output
             } else if (sectionId === 'fees') {
                 renderFeesList(); // Ensure fees list is fresh
                 renderPaymentsList(); // Ensure payments list is fresh
                 populateFeesDropdowns(); // Ensure fees dropdowns are fresh
                 resetForm('fee-setup-form'); // Reset forms
                 resetForm('fee-payment-form');
             } else if (sectionId === 'users') {
                 renderUsersList(); // Ensure users list is fresh
                 populateUserDropdowns(); // Ensure users dropdowns are fresh (if any are used)
                 resetForm('user-form'); // Reset form
             }
        }

    </script>

</body>
</html>