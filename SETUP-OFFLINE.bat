@echo off
title Bridge of Hope Girls' School - Offline Setup
color 0A

echo.
echo ========================================================
echo   Bridge of Hope Girls' School Management System
echo   Offline Dependencies Setup
echo ========================================================
echo.

echo This script will download all required libraries to make
echo the system work without internet connection.
echo.
echo Required libraries:
echo - Bootstrap CSS and JS
echo - Font Awesome icons and fonts
echo - Chart.js for charts and graphs
echo - jsPDF for PDF generation
echo - html2canvas for report generation
echo.

set /p confirm="Do you want to continue? (Y/N): "
if /i "%confirm%" NEQ "Y" (
    echo Setup cancelled.
    pause
    exit /b
)

echo.
echo Starting download...
echo.

:: Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if %errorlevel% NEQ 0 (
    echo ERROR: PowerShell is required but not available.
    echo Please install PowerShell or run this on Windows 7/10/11.
    pause
    exit /b 1
)

:: Run the PowerShell script
echo Running PowerShell download script...
powershell -ExecutionPolicy Bypass -File "download-dependencies.ps1"

if %errorlevel% EQU 0 (
    echo.
    echo ========================================================
    echo   Setup completed successfully!
    echo ========================================================
    echo.
    echo The system is now ready to work offline.
    echo.
    echo Next steps:
    echo 1. Test the setup by opening 'test-offline.html'
    echo 2. If all tests pass, open 'index.html' to use the system
    echo 3. Copy this entire folder to any computer - it will work offline
    echo.
    echo Files created:
    echo - libs\ folder with all dependencies
    echo - All required CSS, JS, and font files
    echo.
) else (
    echo.
    echo ========================================================
    echo   Setup failed!
    echo ========================================================
    echo.
    echo Please check your internet connection and try again.
    echo If the problem persists, try running 'download-dependencies.ps1'
    echo directly in PowerShell.
    echo.
)

echo Press any key to exit...
pause >nul
