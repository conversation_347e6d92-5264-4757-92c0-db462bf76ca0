/* Professional Report Card Styles - A4 Landscape */

/* Print-specific styles */
@media print {
    @page {
        size: A4 landscape;
        margin: 15mm;
    }
    
    body {
        margin: 0;
        padding: 0;
        font-family: 'Times New Roman', serif;
        font-size: 11pt;
        line-height: 1.2;
        color: #000;
        background: white;
    }
    
    .report-card {
        page-break-after: always;
        width: 100%;
        height: 100%;
        margin: 0;
        padding: 0;
        border: none !important;
        box-shadow: none !important;
    }
    
    .no-print {
        display: none !important;
    }
}

/* Screen display styles */
.report-card {
    font-family: 'Times New Roman', serif;
    background: white;
    border: 2px solid #000;
    width: 297mm;
    min-height: 210mm;
    padding: 15mm;
    margin: 20px auto;
    box-sizing: border-box;
    position: relative;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Header styles */
.report-header {
    text-align: center;
    border-bottom: 3px double #000;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.school-logo {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border: 1px solid #ccc;
}

.school-name {
    font-size: 24px;
    font-weight: bold;
    color: #000;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin: 0;
}

.school-address {
    font-size: 12px;
    color: #333;
    margin: 5px 0;
}

.report-title {
    font-size: 18px;
    font-weight: bold;
    color: #000;
    text-decoration: underline;
    margin: 10px 0 0 0;
}

.student-photo-placeholder {
    width: 80px;
    height: 80px;
    border: 2px solid #000;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    text-align: center;
}

/* Student information styles */
.student-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    font-size: 14px;
}

.student-info table {
    width: 100%;
    border-collapse: collapse;
}

.student-info td {
    padding: 5px;
}

.student-info .label {
    font-weight: bold;
    width: 120px;
}

.student-info .value {
    border-bottom: 1px solid #000;
}

/* Grading scale styles */
.grading-scale {
    margin-bottom: 20px;
    border: 2px solid #000;
    padding: 10px;
    background-color: #f9f9f9;
}

.grading-scale h4 {
    margin: 0 0 10px 0;
    text-align: center;
    font-size: 14px;
    font-weight: bold;
}

.grading-scale-items {
    display: flex;
    justify-content: space-around;
    font-size: 12px;
    font-weight: bold;
}

/* Academic performance table styles */
.performance-table {
    width: 100%;
    border-collapse: collapse;
    border: 2px solid #000;
    font-size: 11px;
    margin-bottom: 20px;
}

.performance-table th,
.performance-table td {
    border: 1px solid #000;
    padding: 6px;
    text-align: center;
}

.performance-table th {
    background-color: #e9ecef;
    font-weight: bold;
}

.performance-table .subject-name {
    text-align: left;
    font-weight: bold;
}

.performance-table .semester-header {
    background-color: #e9ecef;
    font-weight: bold;
}

.performance-table .summary-row {
    background-color: #e9ecef;
    font-weight: bold;
    border: 2px solid #000;
}

.performance-table .overall-average {
    background-color: #d4edda;
    font-size: 14px;
}

.performance-table .failing-grade {
    color: #dc3545;
    font-weight: bold;
}

/* Attendance and conduct section styles */
.attendance-conduct {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
}

.attendance-section,
.conduct-section {
    flex: 1;
    border: 2px solid #000;
    padding: 15px;
}

.attendance-section {
    margin-right: 20px;
}

.section-title {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    text-decoration: underline;
}

.attendance-table,
.conduct-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.attendance-table td,
.conduct-table td {
    padding: 5px;
}

.attendance-table .label,
.conduct-table .label {
    font-weight: bold;
}

.attendance-table .value,
.conduct-table .value {
    border-bottom: 1px solid #000;
    text-align: center;
}

/* Comments section styles */
.comments-section {
    margin-bottom: 20px;
    border: 2px solid #000;
    padding: 15px;
}

.comments-content {
    min-height: 60px;
    border: 1px solid #ccc;
    padding: 10px;
    background-color: #fafafa;
    font-size: 12px;
    line-height: 1.4;
}

/* Promotion status styles */
.promotion-status {
    margin-bottom: 20px;
    text-align: center;
    border: 2px solid #000;
    padding: 15px;
    background-color: #f0f8ff;
}

.promotion-status h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: bold;
}

.promotion-status .status {
    font-size: 14px;
    font-weight: bold;
}

.promotion-status .promoted {
    color: #28a745;
}

.promotion-status .pending {
    color: #dc3545;
}

/* Signature section styles */
.signature-section {
    display: flex;
    justify-content: space-between;
    margin-top: 30px;
    font-size: 12px;
}

.signature-box {
    text-align: center;
    width: 30%;
}

.signature-line {
    border-bottom: 2px solid #000;
    margin-bottom: 5px;
    height: 40px;
}

.signature-title {
    font-weight: bold;
}

.signature-name {
    margin-top: 5px;
}

.signature-date {
    margin-top: 10px;
}

/* Footer styles */
.report-footer {
    margin-top: 20px;
    text-align: center;
    font-size: 10px;
    color: #666;
    border-top: 1px solid #ccc;
    padding-top: 10px;
}

/* Utility classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-bold {
    font-weight: bold;
}

.text-red {
    color: #dc3545;
}

.text-green {
    color: #28a745;
}

.bg-light {
    background-color: #f8f9fa;
}

.border-double {
    border: 3px double #000;
}

.border-solid {
    border: 2px solid #000;
}

/* Responsive adjustments for smaller screens */
@media screen and (max-width: 1200px) {
    .report-card {
        width: 95%;
        min-height: auto;
        padding: 10mm;
        margin: 10px auto;
    }
    
    .school-name {
        font-size: 20px;
    }
    
    .performance-table {
        font-size: 10px;
    }
    
    .attendance-conduct {
        flex-direction: column;
    }
    
    .attendance-section {
        margin-right: 0;
        margin-bottom: 20px;
    }
}

@media screen and (max-width: 768px) {
    .report-card {
        width: 100%;
        padding: 5mm;
        margin: 5px auto;
    }
    
    .student-info {
        flex-direction: column;
    }
    
    .student-info > div {
        margin-bottom: 15px;
    }
    
    .grading-scale-items {
        flex-direction: column;
        gap: 5px;
    }
    
    .signature-section {
        flex-direction: column;
        gap: 20px;
    }
    
    .signature-box {
        width: 100%;
    }
}
