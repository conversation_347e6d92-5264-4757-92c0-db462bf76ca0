SCHOOL MANAGEMENT SYSTEM - PORTABLE EDITION
===========================================

DIRECTORY STRUCTURE AND FILE DESCRIPTIONS
==========================================

ROOT DIRECTORY FILES:
--------------------

📄 launcher.html              - MAIN LAUNCHER (START HERE)
                               Interactive launcher with system status checks
                               and multiple launch options

📄 login.html                 - Login page for user authentication
📄 index.html                 - Main application interface
📄 test_system.html           - System compatibility test page

📄 style.css                  - Main stylesheet for the application
📄 storage-compat.js          - Storage compatibility layer for USB/offline use

📄 auth.js                    - User authentication and session management
📄 script.js                  - Main application logic and functionality
📄 attendance.js              - Attendance tracking features
📄 curriculum.js              - Curriculum planning and management
📄 calendar.js                - Calendar and event management
📄 backup.js                  - Data backup and restore functionality
📄 login.js                   - Login form handling

📄 start_server.bat           - Windows batch file to start local server
📄 setup_offline.bat          - Script to download offline libraries

📄 README.txt                 - Comprehensive user manual and troubleshooting
📄 DIRECTORY_STRUCTURE.txt    - This file - explains file organization

IMAGE FILES:
-----------
📄 BOH-LOGO.jpg               - School logo (JPEG format)
📄 BOH-LOGO.svg               - School logo (SVG format)
📄 school-logo.svg            - Alternative school logo

OPTIONAL DIRECTORIES:
--------------------

📁 libs/                      - Offline copies of external libraries
   📄 bootstrap.min.css        - Bootstrap CSS framework
   📄 bootstrap.bundle.min.js  - Bootstrap JavaScript
   📄 fontawesome.min.css      - Font Awesome icons
   📄 chart.min.js             - Chart.js for graphs
   📄 jspdf.min.js             - PDF generation library
   📄 html2canvas.min.js       - HTML to canvas conversion

USAGE INSTRUCTIONS:
==================

FOR FIRST-TIME USERS:
1. Open "launcher.html" in your web browser
2. Follow the on-screen instructions
3. Choose your preferred launch method
4. Login with default credentials (see README.txt)

FOR USB DRIVE DEPLOYMENT:
1. Copy all files to USB drive
2. Maintain the directory structure
3. Use "launcher.html" as entry point
4. Consider running "setup_offline.bat" for offline libraries

FOR TROUBLESHOOTING:
1. Run "test_system.html" to check compatibility
2. Read "README.txt" for detailed troubleshooting
3. Try different browsers if issues occur
4. Use server mode for maximum compatibility

FILE DEPENDENCIES:
=================

CRITICAL FILES (Required for basic operation):
- launcher.html
- login.html
- index.html
- style.css
- storage-compat.js
- auth.js
- script.js
- login.js

FEATURE FILES (Required for specific features):
- attendance.js (for attendance tracking)
- curriculum.js (for curriculum management)
- calendar.js (for calendar features)
- backup.js (for data backup/restore)

OPTIONAL FILES (Enhance functionality):
- test_system.html (for system testing)
- start_server.bat (for easy server startup)
- setup_offline.bat (for offline setup)
- libs/ directory (for offline operation)

EXTERNAL DEPENDENCIES:
=====================

The system relies on these external libraries (loaded from CDN with offline fallbacks):

1. Bootstrap 5.1.3 (CSS framework)
   - Online: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/
   - Offline: libs/bootstrap.min.css, libs/bootstrap.bundle.min.js

2. Font Awesome 6.0.0 (Icons)
   - Online: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/
   - Offline: libs/fontawesome.min.css

3. Chart.js 3.7.0 (Charts and graphs)
   - Online: https://cdn.jsdelivr.net/npm/chart.js@3.7.0/
   - Offline: libs/chart.min.js

4. jsPDF 2.5.1 (PDF generation)
   - Online: https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/
   - Offline: libs/jspdf.min.js

5. html2canvas 1.4.1 (HTML to image conversion)
   - Online: https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/
   - Offline: libs/html2canvas.min.js

BACKUP RECOMMENDATIONS:
======================

ALWAYS BACKUP:
- The entire directory structure
- Exported data files from the system
- Any customized configuration

BACKUP LOCATIONS:
- Cloud storage (Google Drive, OneDrive, etc.)
- Multiple USB drives
- Network storage
- Local computer backup

SECURITY CONSIDERATIONS:
=======================

DEFAULT PASSWORDS:
- Change all default passwords after first login
- Use strong passwords for production use
- Regularly update user credentials

DATA PROTECTION:
- Student data is stored locally in browser storage
- No data is transmitted over the internet
- Regular backups protect against data loss
- Consider encryption for sensitive deployments

SUPPORT INFORMATION:
===================

For technical support:
Email: <EMAIL>

When reporting issues, include:
- Browser name and version
- Operating system
- Error messages (if any)
- Steps to reproduce the problem
- Whether using server mode or direct file access

VERSION HISTORY:
===============

v2.0 Portable Edition:
- Added storage compatibility layer
- Improved USB drive support
- Added offline fallbacks
- Enhanced cross-browser compatibility
- Added comprehensive launcher system
- Improved error handling

v1.0 Original Edition:
- Basic school management functionality
- Grade tracking and report generation
- User authentication system
- Data persistence using localStorage

==========================================
End of Directory Structure Documentation
==========================================
