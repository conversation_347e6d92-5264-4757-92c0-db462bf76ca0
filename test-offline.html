<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline Dependencies Test</title>
    
    <!-- Bootstrap CSS - Local -->
    <link href="libs/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome - Local -->
    <link rel="stylesheet" href="libs/fontawesome.min.css">
    
    <style>
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        .test-result {
            font-weight: bold;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">
            <i class="fas fa-school"></i>
            Bridge of Hope Girls' School - Offline Test
        </h1>
        
        <div class="test-section">
            <h3><i class="fas fa-check-circle"></i> Bootstrap Test</h3>
            <p>If you can see this styled card with proper Bootstrap formatting, Bootstrap CSS is working.</p>
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Bootstrap Card</h5>
                    <p class="card-text">This is a Bootstrap card component.</p>
                    <button class="btn btn-primary" onclick="testBootstrapJS()">Test Bootstrap JS</button>
                </div>
            </div>
            <div id="bootstrap-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-icons"></i> Font Awesome Test</h3>
            <p>If you can see icons below, Font Awesome is working:</p>
            <div class="row">
                <div class="col-md-3 text-center">
                    <i class="fas fa-user fa-3x text-primary"></i>
                    <p>Solid Icons</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="far fa-heart fa-3x text-danger"></i>
                    <p>Regular Icons</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fab fa-github fa-3x text-dark"></i>
                    <p>Brand Icons</p>
                </div>
                <div class="col-md-3 text-center">
                    <i class="fas fa-graduation-cap fa-3x text-success"></i>
                    <p>Education Icons</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-chart-bar"></i> Chart.js Test</h3>
            <p>Testing Chart.js library:</p>
            <canvas id="testChart" width="400" height="200"></canvas>
            <div id="chart-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-file-pdf"></i> PDF Libraries Test</h3>
            <p>Testing jsPDF and html2canvas libraries:</p>
            <button class="btn btn-success" onclick="testPDFLibraries()">Test PDF Generation</button>
            <div id="pdf-result" class="test-result"></div>
        </div>
        
        <div class="test-section">
            <h3><i class="fas fa-wifi"></i> Connection Status</h3>
            <div id="connection-status" class="test-result"></div>
        </div>
    </div>

    <!-- Bootstrap JS - Local -->
    <script src="libs/bootstrap.bundle.min.js"></script>
    
    <!-- Chart.js - Local -->
    <script src="libs/chart.min.js"></script>
    
    <!-- jsPDF - Local -->
    <script src="libs/jspdf.min.js"></script>
    
    <!-- html2canvas - Local -->
    <script src="libs/html2canvas.min.js"></script>

    <script>
        // Test Bootstrap JS
        function testBootstrapJS() {
            try {
                // Test if Bootstrap is available
                if (typeof bootstrap !== 'undefined') {
                    document.getElementById('bootstrap-result').innerHTML = 
                        '<span class="success">✓ Bootstrap JS is working!</span>';
                    
                    // Show a toast notification
                    const toastHTML = `
                        <div class="toast position-fixed top-0 end-0 m-3" role="alert">
                            <div class="toast-header">
                                <strong class="me-auto">Bootstrap Test</strong>
                                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                            </div>
                            <div class="toast-body">
                                Bootstrap JavaScript is working perfectly!
                            </div>
                        </div>
                    `;
                    document.body.insertAdjacentHTML('beforeend', toastHTML);
                    const toast = new bootstrap.Toast(document.querySelector('.toast'));
                    toast.show();
                } else {
                    throw new Error('Bootstrap not found');
                }
            } catch (error) {
                document.getElementById('bootstrap-result').innerHTML = 
                    '<span class="error">✗ Bootstrap JS failed: ' + error.message + '</span>';
            }
        }

        // Test Chart.js
        function testChart() {
            try {
                if (typeof Chart !== 'undefined') {
                    const ctx = document.getElementById('testChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'bar',
                        data: {
                            labels: ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4'],
                            datasets: [{
                                label: 'Students',
                                data: [25, 30, 28, 32],
                                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 1
                            }]
                        },
                        options: {
                            responsive: true,
                            scales: {
                                y: {
                                    beginAtZero: true
                                }
                            }
                        }
                    });
                    document.getElementById('chart-result').innerHTML = 
                        '<span class="success">✓ Chart.js is working!</span>';
                } else {
                    throw new Error('Chart.js not found');
                }
            } catch (error) {
                document.getElementById('chart-result').innerHTML = 
                    '<span class="error">✗ Chart.js failed: ' + error.message + '</span>';
            }
        }

        // Test PDF libraries
        function testPDFLibraries() {
            try {
                let results = [];
                
                // Test jsPDF
                if (typeof window.jspdf !== 'undefined') {
                    results.push('<span class="success">✓ jsPDF is available</span>');
                } else {
                    results.push('<span class="error">✗ jsPDF not found</span>');
                }
                
                // Test html2canvas
                if (typeof html2canvas !== 'undefined') {
                    results.push('<span class="success">✓ html2canvas is available</span>');
                } else {
                    results.push('<span class="error">✗ html2canvas not found</span>');
                }
                
                document.getElementById('pdf-result').innerHTML = results.join('<br>');
            } catch (error) {
                document.getElementById('pdf-result').innerHTML = 
                    '<span class="error">✗ PDF libraries test failed: ' + error.message + '</span>';
            }
        }

        // Check connection status
        function checkConnection() {
            const status = navigator.onLine ? 'Online' : 'Offline';
            const statusClass = navigator.onLine ? 'success' : 'error';
            const icon = navigator.onLine ? 'fas fa-wifi' : 'fas fa-wifi-slash';
            
            document.getElementById('connection-status').innerHTML = 
                `<i class="${icon}"></i> <span class="${statusClass}">Connection Status: ${status}</span>`;
        }

        // Initialize tests when page loads
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
            testChart();
            testPDFLibraries();
            
            // Update connection status when it changes
            window.addEventListener('online', checkConnection);
            window.addEventListener('offline', checkConnection);
        });
    </script>
</body>
</html>
