// Curriculum and Lesson Planning for School Grade Management System

// Initialize curriculum UI
function initializeCurriculumUI() {
    // Populate class dropdowns
    populateCurriculumClassDropdowns();
}

// Populate curriculum class dropdowns
function populateCurriculumClassDropdowns() {
    const curriculumClass = document.getElementById('curriculumClass');
    const viewCurriculumClass = document.getElementById('viewCurriculumClass');
    
    if (curriculumClass) {
        // Clear existing options except the first one
        curriculumClass.innerHTML = '<option value="">Select Class</option>';
        
        // Add class options
        schoolData.classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            curriculumClass.appendChild(option);
        });
    }
    
    if (viewCurriculumClass) {
        // Clear existing options except the first one
        viewCurriculumClass.innerHTML = '<option value="">Select Class</option>';
        
        // Add class options
        schoolData.classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            viewCurriculumClass.appendChild(option);
        });
    }
}

// Handle curriculum class change
function handleCurriculumClassChange(event) {
    const selectedClass = event.target.value;
    const subjectSelect = document.getElementById('curriculumSubject');
    
    if (!subjectSelect) return;
    
    // Clear existing options
    subjectSelect.innerHTML = '<option value="">Select Subject</option>';
    
    // Populate subjects based on selected class
    if (selectedClass && schoolData.subjects[selectedClass]) {
        schoolData.subjects[selectedClass].forEach(subject => {
            const option = document.createElement('option');
            option.value = subject;
            option.textContent = subject;
            subjectSelect.appendChild(option);
        });
    }
}

// Handle curriculum submit
function handleCurriculumSubmit(event) {
    event.preventDefault();
    
    const classValue = document.getElementById('curriculumClass').value;
    const subject = document.getElementById('curriculumSubject').value;
    const topic = document.getElementById('curriculumTopic').value;
    const description = document.getElementById('curriculumDescription').value;
    
    if (!classValue || !subject || !topic) {
        alert('Please select class, subject, and enter a topic.');
        return;
    }
    
    try {
        // Create curriculum entry
        const curriculumEntry = {
            id: `curriculum_${Date.now()}`,
            class: classValue,
            subject: subject,
            topic: topic,
            description: description,
            createdBy: JSON.parse(sessionStorage.getItem('userSession'))?.username || 'unknown',
            createdAt: new Date().toISOString(),
            lastModified: new Date().toISOString()
        };
        
        // Add to curriculum array
        schoolData.curriculum.push(curriculumEntry);
        
        // Save data
        saveData();
        
        // Clear form
        document.getElementById('curriculumTopic').value = '';
        document.getElementById('curriculumDescription').value = '';
        
        // Update curriculum overview if the same class is selected
        const viewCurriculumClass = document.getElementById('viewCurriculumClass');
        if (viewCurriculumClass && viewCurriculumClass.value === classValue) {
            handleViewCurriculumClassChange({ target: viewCurriculumClass });
        }
        
        alert('Curriculum entry added successfully!');
    } catch (error) {
        console.error('Error adding curriculum entry:', error);
        alert('Failed to add curriculum entry. Error: ' + error.message);
    }
}

// Handle view curriculum class change
function handleViewCurriculumClassChange(event) {
    const selectedClass = event.target.value;
    const curriculumOverview = document.getElementById('curriculumOverview');
    
    if (!curriculumOverview) return;
    
    if (!selectedClass) {
        curriculumOverview.innerHTML = '<p class="text-center text-muted">Select a class to view curriculum</p>';
        return;
    }
    
    // Get curriculum entries for the selected class
    const classEntries = schoolData.curriculum.filter(entry => entry.class === selectedClass);
    
    if (classEntries.length === 0) {
        curriculumOverview.innerHTML = '<p class="text-center text-muted">No curriculum entries found for this class.</p>';
        return;
    }
    
    // Group entries by subject
    const entriesBySubject = {};
    classEntries.forEach(entry => {
        if (!entriesBySubject[entry.subject]) {
            entriesBySubject[entry.subject] = [];
        }
        entriesBySubject[entry.subject].push(entry);
    });
    
    // Create accordion for subjects
    const accordion = document.createElement('div');
    accordion.classList.add('accordion');
    accordion.id = 'curriculumAccordion';
    
    let index = 0;
    for (const subject in entriesBySubject) {
        const accordionItem = document.createElement('div');
        accordionItem.classList.add('accordion-item');
        
        const headerId = `heading${index}`;
        const collapseId = `collapse${index}`;
        
        accordionItem.innerHTML = `
            <h2 class="accordion-header" id="${headerId}">
                <button class="accordion-button ${index > 0 ? 'collapsed' : ''}" type="button" 
                        data-bs-toggle="collapse" data-bs-target="#${collapseId}" 
                        aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="${collapseId}">
                    ${subject} (${entriesBySubject[subject].length} topics)
                </button>
            </h2>
            <div id="${collapseId}" class="accordion-collapse collapse ${index === 0 ? 'show' : ''}" 
                 aria-labelledby="${headerId}" data-bs-parent="#curriculumAccordion">
                <div class="accordion-body">
                    <div class="list-group">
                        ${entriesBySubject[subject].map(entry => `
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">${entry.topic}</h5>
                                    <small>Added: ${new Date(entry.createdAt).toLocaleDateString()}</small>
                                </div>
                                <p class="mb-1">${entry.description}</p>
                                <div class="d-flex justify-content-end mt-2">
                                    <button class="btn btn-sm btn-outline-primary me-2 edit-curriculum-btn" 
                                            data-entry-id="${entry.id}">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger remove-curriculum-btn" 
                                            data-entry-id="${entry.id}">
                                        <i class="fas fa-trash-alt"></i> Remove
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
        
        accordion.appendChild(accordionItem);
        index++;
    }
    
    // Clear and add accordion
    curriculumOverview.innerHTML = '';
    curriculumOverview.appendChild(accordion);
    
    // Add event listeners to edit and remove buttons
    const editButtons = curriculumOverview.querySelectorAll('.edit-curriculum-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', handleEditCurriculum);
    });
    
    const removeButtons = curriculumOverview.querySelectorAll('.remove-curriculum-btn');
    removeButtons.forEach(button => {
        button.addEventListener('click', handleRemoveCurriculum);
    });
}

// Handle edit curriculum
function handleEditCurriculum(event) {
    const entryId = event.currentTarget.dataset.entryId;
    const entry = schoolData.curriculum.find(e => e.id === entryId);
    
    if (!entry) {
        alert('Curriculum entry not found.');
        return;
    }
    
    // Create modal for editing
    const modalId = 'editCurriculumModal';
    let modal = document.getElementById(modalId);
    
    // Remove existing modal if it exists
    if (modal) {
        document.body.removeChild(modal);
    }
    
    // Create new modal
    modal = document.createElement('div');
    modal.id = modalId;
    modal.classList.add('modal', 'fade');
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'editCurriculumModalLabel');
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editCurriculumModalLabel">Edit Curriculum Entry</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editCurriculumForm">
                        <div class="mb-3">
                            <label for="editCurriculumTopic" class="form-label">Topic</label>
                            <input type="text" class="form-control" id="editCurriculumTopic" value="${entry.topic}">
                        </div>
                        <div class="mb-3">
                            <label for="editCurriculumDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="editCurriculumDescription" rows="3">${entry.description}</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveCurriculumBtn">Save Changes</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Initialize modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // Add event listener to save button
    const saveBtn = document.getElementById('saveCurriculumBtn');
    saveBtn.addEventListener('click', () => {
        const topic = document.getElementById('editCurriculumTopic').value;
        const description = document.getElementById('editCurriculumDescription').value;
        
        if (!topic) {
            alert('Please enter a topic.');
            return;
        }
        
        // Update entry
        const entryIndex = schoolData.curriculum.findIndex(e => e.id === entryId);
        if (entryIndex !== -1) {
            schoolData.curriculum[entryIndex].topic = topic;
            schoolData.curriculum[entryIndex].description = description;
            schoolData.curriculum[entryIndex].lastModified = new Date().toISOString();
            
            // Save data
            saveData();
            
            // Update curriculum overview
            handleViewCurriculumClassChange({ target: document.getElementById('viewCurriculumClass') });
            
            // Hide modal
            modalInstance.hide();
            
            alert('Curriculum entry updated successfully!');
        }
    });
}

// Handle remove curriculum
function handleRemoveCurriculum(event) {
    const entryId = event.currentTarget.dataset.entryId;
    
    if (confirm('Are you sure you want to remove this curriculum entry?')) {
        // Remove entry
        schoolData.curriculum = schoolData.curriculum.filter(entry => entry.id !== entryId);
        
        // Save data
        saveData();
        
        // Update curriculum overview
        handleViewCurriculumClassChange({ target: document.getElementById('viewCurriculumClass') });
        
        alert('Curriculum entry removed successfully!');
    }
}
