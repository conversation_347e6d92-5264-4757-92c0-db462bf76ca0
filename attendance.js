// Attendance Management for School Grade Management System

// Initialize attendance UI
function initializeAttendanceUI() {
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    const attendanceDate = document.getElementById('attendanceDate');
    if (attendanceDate) {
        attendanceDate.value = today;
    }
    
    // Populate class dropdowns
    populateAttendanceClassDropdowns();
}

// Populate attendance class dropdowns
function populateAttendanceClassDropdowns() {
    const attendanceClass = document.getElementById('attendanceClass');
    const reportClass = document.getElementById('reportClass');
    
    if (attendanceClass) {
        // Clear existing options except the first one
        attendanceClass.innerHTML = '<option value="">Select Class</option>';
        
        // Add class options
        schoolData.classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            attendanceClass.appendChild(option);
        });
    }
    
    if (reportClass) {
        // Clear existing options except the first one
        reportClass.innerHTML = '<option value="">Select Class</option>';
        
        // Add class options
        schoolData.classes.forEach(className => {
            const option = document.createElement('option');
            option.value = className;
            option.textContent = className;
            reportClass.appendChild(option);
        });
    }
}

// Handle attendance form submit
function handleAttendanceFormSubmit(event) {
    event.preventDefault();
    
    const classValue = document.getElementById('attendanceClass').value;
    const dateValue = document.getElementById('attendanceDate').value;
    
    if (!classValue || !dateValue) {
        alert('Please select both class and date.');
        return;
    }
    
    // Get students in the selected class
    const studentsInClass = schoolData.students.filter(student => student.class === classValue);
    
    if (studentsInClass.length === 0) {
        alert('No students found in this class.');
        return;
    }
    
    // Display attendance list
    displayAttendanceList(studentsInClass, dateValue);
    
    // Show save button
    document.getElementById('saveAttendanceBtn').classList.remove('d-none');
}

// Display attendance list
function displayAttendanceList(students, date) {
    const attendanceList = document.getElementById('attendanceList');
    
    if (!attendanceList) return;
    
    // Clear existing content
    attendanceList.innerHTML = '';
    
    // Create attendance table
    const table = document.createElement('table');
    table.classList.add('table', 'table-striped', 'table-bordered');
    table.innerHTML = `
        <thead>
            <tr>
                <th>Student Name</th>
                <th>Status</th>
                <th>Notes</th>
            </tr>
        </thead>
        <tbody>
            <!-- Student rows will be added here -->
        </tbody>
    `;
    
    const tbody = table.querySelector('tbody');
    
    // Get existing attendance records for this date
    const existingRecords = schoolData.attendance.filter(record => record.date === date);
    
    // Add student rows
    students.forEach(student => {
        const existingRecord = existingRecords.find(record => record.studentId === student.id);
        
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${student.name}</td>
            <td>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="status_${student.id}" 
                           id="present_${student.id}" value="present" 
                           ${existingRecord?.status === 'present' || !existingRecord ? 'checked' : ''}>
                    <label class="form-check-label" for="present_${student.id}">Present</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="status_${student.id}" 
                           id="absent_${student.id}" value="absent"
                           ${existingRecord?.status === 'absent' ? 'checked' : ''}>
                    <label class="form-check-label" for="absent_${student.id}">Absent</label>
                </div>
                <div class="form-check form-check-inline">
                    <input class="form-check-input" type="radio" name="status_${student.id}" 
                           id="late_${student.id}" value="late"
                           ${existingRecord?.status === 'late' ? 'checked' : ''}>
                    <label class="form-check-label" for="late_${student.id}">Late</label>
                </div>
            </td>
            <td>
                <input type="text" class="form-control form-control-sm" 
                       id="notes_${student.id}" placeholder="Optional notes"
                       value="${existingRecord?.notes || ''}">
            </td>
        `;
        
        tbody.appendChild(tr);
    });
    
    attendanceList.appendChild(table);
}

// Handle save attendance
function handleSaveAttendance() {
    const classValue = document.getElementById('attendanceClass').value;
    const dateValue = document.getElementById('attendanceDate').value;
    
    if (!classValue || !dateValue) {
        alert('Please select both class and date.');
        return;
    }
    
    try {
        // Get students in the selected class
        const studentsInClass = schoolData.students.filter(student => student.class === classValue);
        
        // Remove existing attendance records for this class and date
        schoolData.attendance = schoolData.attendance.filter(
            record => !(record.date === dateValue && studentsInClass.some(s => s.id === record.studentId))
        );
        
        // Add new attendance records
        studentsInClass.forEach(student => {
            const statusRadios = document.getElementsByName(`status_${student.id}`);
            let selectedStatus = 'present'; // Default
            
            // Find selected status
            for (const radio of statusRadios) {
                if (radio.checked) {
                    selectedStatus = radio.value;
                    break;
                }
            }
            
            const notes = document.getElementById(`notes_${student.id}`).value;
            
            // Create attendance record
            const attendanceRecord = {
                id: `attendance_${Date.now()}_${student.id}`,
                studentId: student.id,
                class: classValue,
                date: dateValue,
                status: selectedStatus,
                notes: notes,
                recordedBy: JSON.parse(sessionStorage.getItem('userSession'))?.username || 'unknown',
                recordedAt: new Date().toISOString()
            };
            
            // Add to attendance array
            schoolData.attendance.push(attendanceRecord);
        });
        
        // Save data
        saveData();
        
        alert('Attendance saved successfully!');
    } catch (error) {
        console.error('Error saving attendance:', error);
        alert('Failed to save attendance. Error: ' + error.message);
    }
}

// Handle attendance report submit
function handleAttendanceReportSubmit(event) {
    event.preventDefault();
    
    const classValue = document.getElementById('reportClass').value;
    const fromDate = document.getElementById('reportDateFrom').value;
    const toDate = document.getElementById('reportDateTo').value;
    
    if (!classValue || !fromDate || !toDate) {
        alert('Please select class and date range.');
        return;
    }
    
    // Generate and display attendance report
    generateAttendanceReport(classValue, fromDate, toDate);
}

// Generate attendance report
function generateAttendanceReport(classValue, fromDate, toDate) {
    const reportResult = document.getElementById('attendanceReportResult');
    
    if (!reportResult) return;
    
    try {
        // Get students in the selected class
        const studentsInClass = schoolData.students.filter(student => student.class === classValue);
        
        if (studentsInClass.length === 0) {
            reportResult.innerHTML = '<p class="text-center text-muted">No students found in this class.</p>';
            return;
        }
        
        // Get attendance records in the date range
        const attendanceRecords = schoolData.attendance.filter(record => {
            return record.class === classValue && 
                   record.date >= fromDate && 
                   record.date <= toDate;
        });
        
        // Create report
        reportResult.innerHTML = `
            <h5>Attendance Report: ${classValue}</h5>
            <p>Period: ${fromDate} to ${toDate}</p>
        `;
        
        // Create attendance summary table
        const table = document.createElement('table');
        table.classList.add('table', 'table-striped', 'table-bordered');
        table.innerHTML = `
            <thead>
                <tr>
                    <th>Student Name</th>
                    <th>Present</th>
                    <th>Absent</th>
                    <th>Late</th>
                    <th>Attendance Rate</th>
                </tr>
            </thead>
            <tbody>
                <!-- Student rows will be added here -->
            </tbody>
        `;
        
        const tbody = table.querySelector('tbody');
        
        // Calculate date range length (number of school days)
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);
        const daysDiff = Math.round((toDateObj - fromDateObj) / (1000 * 60 * 60 * 24)) + 1;
        
        // Add student rows
        studentsInClass.forEach(student => {
            // Count attendance statuses
            const studentRecords = attendanceRecords.filter(record => record.studentId === student.id);
            const presentCount = studentRecords.filter(record => record.status === 'present').length;
            const absentCount = studentRecords.filter(record => record.status === 'absent').length;
            const lateCount = studentRecords.filter(record => record.status === 'late').length;
            
            // Calculate attendance rate
            const attendanceRate = studentRecords.length > 0 ? 
                Math.round((presentCount + (lateCount * 0.5)) / daysDiff * 100) : 0;
            
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${student.name}</td>
                <td>${presentCount}</td>
                <td>${absentCount}</td>
                <td>${lateCount}</td>
                <td>${attendanceRate}%</td>
            `;
            
            tbody.appendChild(tr);
        });
        
        reportResult.appendChild(table);
        
        // Add export button
        const exportBtn = document.createElement('button');
        exportBtn.classList.add('btn', 'btn-success', 'mt-3');
        exportBtn.innerHTML = '<i class="fas fa-file-excel me-2"></i> Export to Excel';
        exportBtn.addEventListener('click', () => exportAttendanceReport(classValue, fromDate, toDate));
        
        reportResult.appendChild(exportBtn);
    } catch (error) {
        console.error('Error generating attendance report:', error);
        reportResult.innerHTML = `<p class="text-danger">Error generating report: ${error.message}</p>`;
    }
}

// Export attendance report to Excel
function exportAttendanceReport(classValue, fromDate, toDate) {
    try {
        // Get students in the selected class
        const studentsInClass = schoolData.students.filter(student => student.class === classValue);
        
        // Get attendance records in the date range
        const attendanceRecords = schoolData.attendance.filter(record => {
            return record.class === classValue && 
                   record.date >= fromDate && 
                   record.date <= toDate;
        });
        
        // Calculate date range length
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);
        const daysDiff = Math.round((toDateObj - fromDateObj) / (1000 * 60 * 60 * 24)) + 1;
        
        // Create CSV content
        let csvContent = 'Student Name,Present,Absent,Late,Attendance Rate\n';
        
        // Add student rows
        studentsInClass.forEach(student => {
            // Count attendance statuses
            const studentRecords = attendanceRecords.filter(record => record.studentId === student.id);
            const presentCount = studentRecords.filter(record => record.status === 'present').length;
            const absentCount = studentRecords.filter(record => record.status === 'absent').length;
            const lateCount = studentRecords.filter(record => record.status === 'late').length;
            
            // Calculate attendance rate
            const attendanceRate = studentRecords.length > 0 ? 
                Math.round((presentCount + (lateCount * 0.5)) / daysDiff * 100) : 0;
            
            csvContent += `${student.name},${presentCount},${absentCount},${lateCount},${attendanceRate}%\n`;
        });
        
        // Create a download link
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `Attendance_${classValue}_${fromDate}_to_${toDate}.csv`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        alert('Attendance report exported successfully!');
    } catch (error) {
        console.error('Error exporting attendance report:', error);
        alert('Failed to export attendance report. Error: ' + error.message);
    }
}
