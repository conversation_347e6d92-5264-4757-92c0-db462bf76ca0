# Bridge of Hope Girls' School Management System
# PowerShell script to download offline dependencies

Write-Host "Bridge of Hope Girls' School Management System" -ForegroundColor Green
Write-Host "Downloading offline dependencies..." -ForegroundColor Yellow
Write-Host ""

# Create libs directory if it doesn't exist
if (!(Test-Path "libs")) {
    New-Item -ItemType Directory -Path "libs"
    Write-Host "Created libs directory" -ForegroundColor Green
}

# Function to download file with progress
function Download-File {
    param(
        [string]$Url,
        [string]$OutputPath,
        [string]$Description
    )
    
    try {
        Write-Host "Downloading $Description..." -ForegroundColor Cyan
        Invoke-WebRequest -Uri $Url -OutFile $OutputPath -UseBasicParsing
        Write-Host "✓ $Description downloaded successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "✗ Failed to download $Description" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Download Bootstrap CSS
Download-File -Url "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" -OutputPath "libs\bootstrap.min.css" -Description "Bootstrap CSS"

# Download Bootstrap JS
Download-File -Url "https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" -OutputPath "libs\bootstrap.bundle.min.js" -Description "Bootstrap JS"

# Download Font Awesome CSS
Download-File -Url "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" -OutputPath "libs\fontawesome.min.css" -Description "Font Awesome CSS"

# Download jsPDF
Download-File -Url "https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js" -OutputPath "libs\jspdf.min.js" -Description "jsPDF"

# Download html2canvas
Download-File -Url "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js" -OutputPath "libs\html2canvas.min.js" -Description "html2canvas"

# Download Chart.js
Download-File -Url "https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js" -OutputPath "libs\chart.min.js" -Description "Chart.js"

# Create webfonts directory for Font Awesome
if (!(Test-Path "libs\webfonts")) {
    New-Item -ItemType Directory -Path "libs\webfonts"
    Write-Host "Created webfonts directory" -ForegroundColor Green
}

# Download Font Awesome fonts
Download-File -Url "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2" -OutputPath "libs\webfonts\fa-solid-900.woff2" -Description "Font Awesome Solid"
Download-File -Url "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-regular-400.woff2" -OutputPath "libs\webfonts\fa-regular-400.woff2" -Description "Font Awesome Regular"
Download-File -Url "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-brands-400.woff2" -OutputPath "libs\webfonts\fa-brands-400.woff2" -Description "Font Awesome Brands"

Write-Host ""
Write-Host "Fixing Font Awesome CSS paths..." -ForegroundColor Yellow

# Fix Font Awesome CSS to use local fonts
try {
    $fontAwesomeCSS = Get-Content "libs\fontawesome.min.css" -Raw
    $fontAwesomeCSS = $fontAwesomeCSS -replace 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/', './webfonts/'
    $fontAwesomeCSS | Set-Content "libs\fontawesome.min.css"
    Write-Host "✓ Font Awesome CSS paths fixed!" -ForegroundColor Green
}
catch {
    Write-Host "✗ Failed to fix Font Awesome CSS paths" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Setup complete! The system should now work offline." -ForegroundColor Green
Write-Host ""
Write-Host "Files downloaded to 'libs' directory:" -ForegroundColor Cyan
Get-ChildItem "libs" -Recurse | ForEach-Object { Write-Host "  $($_.FullName)" -ForegroundColor Gray }

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
