@echo off
title School Management System - Local Server
echo ========================================
echo School Management System - Local Server
echo ========================================
echo.

cd /d "%~dp0"

echo Checking for Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python not found! Trying python3...
    python3 --version >nul 2>&1
    if %errorlevel% neq 0 (
        echo.
        echo ERROR: Python is not installed or not in PATH
        echo.
        echo Please install Python from https://python.org
        echo Or try running the system directly by opening launcher.html
        echo.
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=python3
    )
) else (
    set PYTHON_CMD=python
)

echo Python found! Starting local server...
echo.
echo Server will start on: http://localhost:8000
echo.
echo IMPORTANT: 
echo - Keep this window open while using the system
echo - Open your browser and go to: http://localhost:8000
echo - Press Ctrl+C to stop the server
echo.

timeout /t 3 /nobreak >nul

echo Starting server...
start "" "http://localhost:8000"

%PYTHON_CMD% -m http.server 8000

echo.
echo Server stopped.
pause
