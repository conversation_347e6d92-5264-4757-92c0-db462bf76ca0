================================================================================
                    SCHOOL GRADE MANAGEMENT SYSTEM - PORTABLE EDITION
================================================================================

OVERVIEW
--------
This is a complete school management system that runs entirely in your web 
browser without requiring any installation. It can be run from a USB drive, 
network folder, or any location on your computer.

FEATURES
--------
✓ Student grade management and professional report cards
✓ Professional A4 landscape report cards with comprehensive grading
✓ Attendance tracking with detailed records
✓ Fee management and payment tracking
✓ Parent and teacher management
✓ Curriculum planning and management
✓ Calendar and event management
✓ Data backup and restore functionality
✓ Multi-user authentication system
✓ Completely offline operation
✓ Professional PDF generation for reports
✓ Print-optimized layouts for all documents

GETTING STARTED
---------------
1. Open "launcher.html" in your web browser
2. Choose your preferred launch method
3. Use the default login credentials:
   - Admin: admin / admin123
   - Teacher: teacher / teacher123
   - Parent: parent / parent123

BROWSER COMPATIBILITY
--------------------
✓ EXCELLENT: Firefox (recommended for USB/file system use)
✓ GOOD: Chrome (may need server mode for data persistence)
✓ GOOD: Microsoft Edge (may need server mode for data persistence)
✓ GOOD: Safari (macOS)

RUNNING FROM USB DRIVE
----------------------
For best results when running from a USB drive:

1. FIREFOX USERS (Recommended):
   - Simply open launcher.html
   - Click "Launch System"
   - Your data will be saved automatically

2. CHROME/EDGE USERS:
   - Option A: Use launcher.html and accept limited data persistence
   - Option B: Use local server mode for full functionality
   
3. LOCAL SERVER MODE:
   - Double-click "start_server.bat" (Windows)
   - Or manually run: python -m http.server 8000
   - Open browser to: http://localhost:8000

DATA STORAGE EXPLAINED
----------------------
The system uses multiple storage methods for maximum compatibility:

1. LOCAL STORAGE (Best): Full data persistence, works in most browsers
2. SESSION STORAGE: Data persists during browser session only
3. COOKIE STORAGE: Limited storage, but works in restricted environments
4. MEMORY STORAGE: Temporary storage, lost on page refresh

The system automatically chooses the best available storage method.

IMPORTANT DATA BACKUP INFORMATION
---------------------------------
⚠️  CRITICAL: Your data is stored in your browser's storage system.

BACKUP REGULARLY:
- Use the "System Settings" → "Backup & Restore" feature
- Export your data frequently
- Keep backup files in a safe location
- Test restore functionality periodically

DATA PERSISTENCE RULES:
- Always use the same browser and same access method
- Don't clear browser data if you want to keep your information
- USB drive letter changes may affect data access in some browsers
- Server mode provides the most reliable data persistence

TROUBLESHOOTING
---------------

PROBLEM: System looks broken, missing styles
SOLUTION: 
- Check if all files are in the same folder
- Try refreshing the page (F5)
- Use Firefox browser
- Try server mode

PROBLEM: Data disappears after refresh
SOLUTION:
- Use Firefox browser for better file:// protocol support
- Use server mode (start_server.bat)
- Check browser privacy settings
- Don't use incognito/private browsing mode

PROBLEM: Can't save data
SOLUTION:
- Check browser console for errors (F12)
- Ensure JavaScript is enabled
- Try different browser
- Use server mode

PROBLEM: Server won't start
SOLUTION:
- Install Python from https://python.org
- Run as administrator
- Check if port 8000 is available
- Try different port: python -m http.server 8080

PROBLEM: Slow performance
SOLUTION:
- Close other browser tabs
- Clear browser cache
- Restart browser
- Use modern browser version

FILE STRUCTURE
--------------
launcher.html          - Main launcher (START HERE)
login.html            - Login page
index.html            - Main application
demo-report-card.html - Professional report card demo
style.css             - Styling
report-card-styles.css - Professional report card styles
storage-compat.js     - Storage compatibility layer
auth.js               - Authentication system
script.js             - Main application logic
attendance.js         - Attendance management
curriculum.js         - Curriculum planning
calendar.js           - Calendar functionality
backup.js             - Backup and restore
start_server.bat      - Windows server launcher
README.txt            - This file

SYSTEM REQUIREMENTS
-------------------
- Modern web browser (2020 or newer)
- JavaScript enabled
- Minimum 50MB free space
- No internet connection required

DEFAULT LOGIN CREDENTIALS
--------------------------
Administrator:
  Username: admin
  Password: admin123
  Access: Full system access

Teacher:
  Username: teacher  
  Password: teacher123
  Access: Grade entry, attendance, reports

Parent:
  Username: parent
  Password: parent123
  Access: View student information only

⚠️  SECURITY NOTE: Change default passwords after first login!

ADVANCED USAGE
--------------

CUSTOM SERVER SETUP:
If you have Node.js installed, you can use:
  npx http-server -p 8000

For PHP users:
  php -S localhost:8000

NETWORK SHARING:
To share across network:
  python -m http.server 8000 --bind 0.0.0.0
  Then access via: http://[computer-ip]:8000

PORTABLE BROWSER:
Consider including a portable Firefox on your USB drive for maximum 
compatibility across different computers.

SUPPORT AND CONTACT
-------------------
For technical support or questions:
Email: <EMAIL>

Include the following information when reporting issues:
- Browser name and version
- Operating system
- Error messages (if any)
- Steps to reproduce the problem
- Whether you're using server mode or direct file access

VERSION INFORMATION
-------------------
Version: 2.0 Portable Edition
Last Updated: 2024
License: Educational Use

CHANGELOG
---------
v2.0 Portable Edition:
- Added storage compatibility layer
- Improved USB drive support
- Added offline fallbacks for external resources
- Enhanced cross-browser compatibility
- Added comprehensive launcher system
- Improved error handling and user feedback

================================================================================
Thank you for using the School Grade Management System!
================================================================================
