@echo off
title School Management System - Offline Setup
echo ========================================
echo School Management System - Offline Setup
echo ========================================
echo.

cd /d "%~dp0"

echo This script will download offline copies of external libraries
echo for maximum portability and offline functionality.
echo.

if not exist "libs" (
    echo Creating libs directory...
    mkdir libs
)

echo.
echo Downloading required libraries...
echo.

echo Note: This requires an internet connection for the initial setup.
echo After setup, the system will work completely offline.
echo.

echo To manually download the libraries, visit these URLs:
echo.
echo Bootstrap CSS: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css
echo Bootstrap JS: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js
echo Font Awesome: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css
echo Chart.js: https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js
echo jsPDF: https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js
echo html2canvas: https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
echo.

echo Save these files in the 'libs' folder with these names:
echo - bootstrap.min.css
echo - bootstrap.bundle.min.js
echo - fontawesome.min.css
echo - chart.min.js
echo - jspdf.min.js
echo - html2canvas.min.js
echo.

echo For automatic download (requires curl or PowerShell):
echo.
choice /c YN /m "Do you want to attempt automatic download"

if errorlevel 2 goto manual
if errorlevel 1 goto download

:download
echo.
echo Attempting to download libraries...

powershell -Command "& {
    try {
        Write-Host 'Downloading Bootstrap CSS...'
        Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' -OutFile 'libs/bootstrap.min.css'
        
        Write-Host 'Downloading Bootstrap JS...'
        Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js' -OutFile 'libs/bootstrap.bundle.min.js'
        
        Write-Host 'Downloading Chart.js...'
        Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js' -OutFile 'libs/chart.min.js'
        
        Write-Host 'Download completed successfully!'
    } catch {
        Write-Host 'Download failed. Please download manually.'
        Write-Host $_.Exception.Message
    }
}"

echo.
echo Note: Font Awesome, jsPDF, and html2canvas may need to be downloaded manually
echo due to their larger size and licensing considerations.
echo.

goto end

:manual
echo.
echo Please download the libraries manually using the URLs provided above.
echo.

:end
echo.
echo Setup complete! You can now run the system offline.
echo Use launcher.html to start the application.
echo.
pause
