:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #f39c12;
    --danger-color: #e74c3c;
    --dark-color: #2c3e50;
    --light-color: #ecf0f1;
    --text-color: #333;
    --border-color: #ddd;
    --sidebar-width: 250px;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --success-color: #2ecc71;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: var(--text-color);
}

/* Sidebar Styles */
.sidebar {
    background-color: var(--dark-color);
    color: white;
    min-height: 100vh;
    padding: 0;
    position: sticky;
    top: 0;
}

.sidebar-header {
    padding: 20px 15px;
    background-color: rgba(0, 0, 0, 0.2);
    text-align: center;
}

.user-info {
    padding: 10px 15px;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
}

.user-name {
    font-size: 0.9rem;
    font-weight: 600;
}

.user-role {
    font-size: 0.8rem;
    opacity: 0.8;
}

.sidebar .nav-item {
    width: 100%;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-left: 3px solid transparent;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link.active {
    color: white;
    background-color: rgba(255, 255, 255, 0.2);
    border-left: 3px solid var(--primary-color);
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Main Content Styles */
.main-content {
    padding: 20px;
}

.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    border: none;
}

.card-header {
    background-color: white;
    border-bottom: 1px solid var(--border-color);
    padding: 15px 20px;
    font-weight: 600;
}

.card-body {
    padding: 20px;
}

/* Form Styles */
.form-label {
    font-weight: 500;
}

.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid var(--border-color);
    padding: 8px 12px;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.btn {
    border-radius: 4px;
    padding: 8px 16px;
    font-weight: 500;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
}

/* Table Styles */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-color);
    vertical-align: middle;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table td, .table th {
    padding: 12px;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Dashboard Styles */
.dashboard-card {
    text-align: center;
    padding: 20px;
}

.dashboard-card h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--primary-color);
}

/* Report Preview Styles */
.report-preview {
    min-height: 400px;
    padding: 20px;
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        position: static;
        min-height: auto;
    }

    .sidebar-header {
        text-align: left;
    }

    .sidebar .nav {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .sidebar .nav-item {
        width: auto;
    }

    .sidebar .nav-link {
        border-left: none;
        border-bottom: 3px solid transparent;
    }

    .sidebar .nav-link:hover, .sidebar .nav-link.active {
        border-left: none;
        border-bottom: 3px solid var(--primary-color);
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 10px;
    }

    .card-header, .card-body {
        padding: 15px;
    }

    .btn {
        padding: 6px 12px;
    }
}

/* Custom Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Attendance Styles */
.attendance-status {
    display: flex;
    align-items: center;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-present {
    background-color: var(--success-color);
}

.status-absent {
    background-color: var(--danger-color);
}

.status-late {
    background-color: var(--warning-color);
}

/* Calendar Styles */
.calendar-cell {
    height: 100px;
    width: 14.28%;
    vertical-align: top;
    padding: 5px;
    position: relative;
}

.empty-cell {
    background-color: #f8f9fa;
}

.today {
    background-color: #e8f4ff;
}

.date-number {
    font-weight: bold;
    margin-bottom: 5px;
}

.events-container {
    font-size: 0.8rem;
}

.event-item {
    padding: 2px 4px;
    margin-bottom: 2px;
    border-radius: 3px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
}

.event-general {
    background-color: #6c757d;
    color: white;
}

.event-exam {
    background-color: #dc3545;
    color: white;
}

.event-holiday {
    background-color: #28a745;
    color: white;
}

.event-meeting {
    background-color: #17a2b8;
    color: white;
}

.event-activity {
    background-color: #ffc107;
    color: black;
}

.calendar-legend {
    font-size: 0.8rem;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-right: 5px;
    border-radius: 3px;
}

/* Curriculum Styles */
.curriculum-topic {
    font-weight: 600;
    margin-bottom: 5px;
}

.curriculum-description {
    font-size: 0.9rem;
    color: #666;
}

/* Utility Classes */
.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-accent {
    color: var(--accent-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-accent {
    background-color: var(--accent-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}
