<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Gradebook Fix & Test - Bridge of Hope Girls' School</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f8f9fa;
        }
        .fix-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        .warning {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .grade-component {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .grade-input {
            width: 80px;
            text-align: center;
        }
        .progress-custom {
            height: 20px;
            background-color: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-bar-custom {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <h1 class="text-center mb-4">🔧 Advanced Gradebook Fix & Test Tool</h1>
        <p class="text-center text-muted">Diagnose and fix issues with the Advanced Gradebook & Assessment feature</p>

        <!-- System Status -->
        <div class="fix-container">
            <h2><i class="fas fa-heartbeat text-primary"></i> System Status</h2>
            <div id="systemStatus">
                <p class="text-muted">Click "Run System Diagnostic" to check system health...</p>
            </div>
            <button class="btn btn-primary" onclick="runSystemDiagnostic()">
                <i class="fas fa-stethoscope"></i> Run System Diagnostic
            </button>
            <button class="btn btn-success" onclick="initializeSystem()">
                <i class="fas fa-cog"></i> Initialize System
            </button>
        </div>

        <!-- Grade Entry Test -->
        <div class="fix-container">
            <h2><i class="fas fa-edit text-success"></i> Grade Entry Test</h2>
            <div class="row">
                <div class="col-md-6">
                    <h5>Test Grade Entry</h5>
                    <div class="mb-3">
                        <label class="form-label">Student:</label>
                        <select class="form-select" id="testStudent">
                            <option value="">Select Student</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Subject:</label>
                        <select class="form-select" id="testSubject">
                            <option value="">Select Subject</option>
                            <option value="MATHEMATICS">Mathematics</option>
                            <option value="ENGLISH">English</option>
                            <option value="BIBLE">Bible</option>
                            <option value="GEOGRAPHY">Geography</option>
                            <option value="HISTORY">History</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Period:</label>
                        <select class="form-select" id="testPeriod">
                            <option value="">Select Period</option>
                            <option value="1st Pd.">1st Period</option>
                            <option value="2nd Pd.">2nd Period</option>
                            <option value="3rd Pd.">3rd Period</option>
                            <option value="Exam 1">Exam 1</option>
                            <option value="4th Pd.">4th Period</option>
                            <option value="5th Pd.">5th Period</option>
                            <option value="6th Pd.">6th Period</option>
                            <option value="Exam 2">Exam 2</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Grade Components</h5>
                    <div class="grade-component">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><strong>Tests (40 pts):</strong></span>
                            <div>
                                <input type="number" class="form-control grade-input d-inline" id="testsScore" min="0" max="40" value="0">
                                <button class="btn btn-sm btn-primary ms-2" onclick="saveTestScore()">Save</button>
                            </div>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" id="testsProgress" style="width: 0%">0%</div>
                        </div>
                    </div>

                    <div class="grade-component">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><strong>Quizzes (40 pts):</strong></span>
                            <div>
                                <input type="number" class="form-control grade-input d-inline" id="quizzesScore" min="0" max="40" value="0">
                                <button class="btn btn-sm btn-info ms-2" onclick="saveQuizScore()">Save</button>
                            </div>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" id="quizzesProgress" style="width: 0%; background-color: #17a2b8;">0%</div>
                        </div>
                    </div>

                    <div class="grade-component">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><strong>Assignments (10 pts):</strong></span>
                            <div>
                                <input type="number" class="form-control grade-input d-inline" id="assignmentsScore" min="0" max="10" value="0">
                                <button class="btn btn-sm btn-warning ms-2" onclick="saveAssignmentScore()">Save</button>
                            </div>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" id="assignmentsProgress" style="width: 0%; background-color: #ffc107; color: #000;">0%</div>
                        </div>
                    </div>

                    <div class="grade-component">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span><strong>Participation (10 pts):</strong></span>
                            <div>
                                <input type="number" class="form-control grade-input d-inline" id="participationScore" min="0" max="10" value="0">
                                <button class="btn btn-sm btn-success ms-2" onclick="saveParticipationScore()">Save</button>
                            </div>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" id="participationProgress" style="width: 0%; background-color: #28a745;">0%</div>
                        </div>
                    </div>

                    <div class="grade-component bg-light">
                        <div class="d-flex justify-content-between align-items-center">
                            <span><strong>Total Grade:</strong></span>
                            <h4 id="totalGrade" class="text-primary">0/100 (F)</h4>
                        </div>
                        <div class="progress-custom">
                            <div class="progress-bar-custom" id="totalProgress" style="width: 0%; background-color: #6f42c1;">0%</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-3">
                <button class="btn btn-success" onclick="loadGrades()">
                    <i class="fas fa-sync-alt"></i> Load Current Grades
                </button>
                <button class="btn btn-info" onclick="testAllComponents()">
                    <i class="fas fa-vial"></i> Test All Components
                </button>
                <button class="btn btn-warning" onclick="resetTestGrades()">
                    <i class="fas fa-undo"></i> Reset Test Grades
                </button>
            </div>
        </div>

        <!-- Test Results -->
        <div class="fix-container">
            <h2><i class="fas fa-clipboard-check text-info"></i> Test Results</h2>
            <div id="testResults">
                <p class="text-muted">Run tests to see results here...</p>
            </div>
        </div>

        <!-- Data Inspection -->
        <div class="fix-container">
            <h2><i class="fas fa-database text-warning"></i> Data Inspection</h2>
            <button class="btn btn-outline-primary" onclick="inspectPeriodGrades()">
                <i class="fas fa-search"></i> Inspect Period Grades Data
            </button>
            <button class="btn btn-outline-success" onclick="inspectSchoolData()">
                <i class="fas fa-eye"></i> Inspect School Data Structure
            </button>
            <button class="btn btn-outline-danger" onclick="clearAllGrades()">
                <i class="fas fa-trash"></i> Clear All Test Grades
            </button>
            
            <div id="dataInspection" class="mt-3" style="display: none;">
                <h5>Data Structure:</h5>
                <pre id="dataDisplay" class="bg-light p-3 border rounded" style="max-height: 300px; overflow-y: auto;"></pre>
            </div>
        </div>
    </div>

    <!-- Include main script -->
    <script src="script.js"></script>
    
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadStudentOptions();
        });

        function loadStudentOptions() {
            const studentSelect = document.getElementById('testStudent');
            if (typeof schoolData !== 'undefined' && schoolData.students) {
                studentSelect.innerHTML = '<option value="">Select Student</option>';
                schoolData.students.forEach(student => {
                    const option = document.createElement('option');
                    option.value = student.id;
                    option.textContent = `${student.name} (${student.class})`;
                    studentSelect.appendChild(option);
                });
            }
        }

        function runSystemDiagnostic() {
            const statusDiv = document.getElementById('systemStatus');
            let results = [];

            // Check if main functions exist
            const requiredFunctions = [
                'getOrCreatePeriodGrades',
                'savePeriodComponentScore', 
                'calculateStudentPeriodBreakdown',
                'updateStudentGradeBreakdown',
                'initializeAdvancedGradebook'
            ];

            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    results.push(`<div class="test-result success">✅ Function ${funcName} exists</div>`);
                } else {
                    results.push(`<div class="test-result error">❌ Function ${funcName} missing</div>`);
                }
            });

            // Check data structures
            if (typeof schoolData !== 'undefined') {
                results.push(`<div class="test-result success">✅ schoolData object exists</div>`);
                
                if (schoolData.periodGrades) {
                    results.push(`<div class="test-result success">✅ periodGrades array exists (${schoolData.periodGrades.length} records)</div>`);
                } else {
                    results.push(`<div class="test-result warning">⚠️ periodGrades array missing - will be created</div>`);
                }

                if (schoolData.students && schoolData.students.length > 0) {
                    results.push(`<div class="test-result success">✅ Students data exists (${schoolData.students.length} students)</div>`);
                } else {
                    results.push(`<div class="test-result error">❌ No students data found</div>`);
                }
            } else {
                results.push(`<div class="test-result error">❌ schoolData object missing</div>`);
            }

            statusDiv.innerHTML = results.join('');
        }

        function initializeSystem() {
            if (typeof initializeAdvancedGradebook === 'function') {
                const result = initializeAdvancedGradebook();
                if (result) {
                    alert('✅ Advanced Gradebook system initialized successfully!');
                    runSystemDiagnostic();
                    loadStudentOptions();
                } else {
                    alert('❌ Failed to initialize Advanced Gradebook system.');
                }
            } else {
                alert('❌ initializeAdvancedGradebook function not found.');
            }
        }

        function saveTestScore() {
            saveComponentScore('Tests', 'testsScore', 'testsProgress', 40);
        }

        function saveQuizScore() {
            saveComponentScore('Quizzes', 'quizzesScore', 'quizzesProgress', 40);
        }

        function saveAssignmentScore() {
            saveComponentScore('Assignments', 'assignmentsScore', 'assignmentsProgress', 10);
        }

        function saveParticipationScore() {
            saveComponentScore('Participation', 'participationScore', 'participationProgress', 10);
        }

        function saveComponentScore(componentType, inputId, progressId, maxPoints) {
            const studentId = document.getElementById('testStudent').value;
            const subject = document.getElementById('testSubject').value;
            const period = document.getElementById('testPeriod').value;
            const score = parseFloat(document.getElementById(inputId).value);

            if (!studentId || !subject || !period) {
                alert('Please select student, subject, and period first.');
                return;
            }

            if (isNaN(score) || score < 0 || score > maxPoints) {
                alert(`Please enter a valid score between 0 and ${maxPoints}.`);
                return;
            }

            try {
                // Use the main system's save function
                if (typeof savePeriodComponentScore === 'function') {
                    // Create a mock modal for the function
                    const mockModal = document.createElement('div');
                    mockModal.id = 'periodComponentEditorModal';
                    mockModal.innerHTML = `
                        <input type="number" id="componentScore" value="${score}">
                        <input type="text" id="componentNotes" value="Test entry">
                    `;
                    document.body.appendChild(mockModal);

                    savePeriodComponentScore(studentId, subject, period, componentType);
                    
                    document.body.removeChild(mockModal);
                } else {
                    // Fallback: direct data manipulation
                    const periodGrades = getOrCreatePeriodGrades(studentId, subject, period);
                    if (periodGrades) {
                        const componentKey = componentType.toLowerCase();
                        periodGrades[componentKey] = score;
                        
                        // Recalculate total
                        periodGrades.total = (periodGrades.tests || 0) +
                                           (periodGrades.quizzes || 0) +
                                           (periodGrades.assignments || 0) +
                                           (periodGrades.participation || 0);
                        
                        periodGrades.letterGrade = getLetterGrade(periodGrades.total);
                        periodGrades.lastUpdated = new Date().toLocaleDateString();
                        
                        saveData();
                    }
                }

                // Update progress bar
                const percentage = (score / maxPoints) * 100;
                const progressBar = document.getElementById(progressId);
                progressBar.style.width = percentage + '%';
                progressBar.textContent = Math.round(percentage) + '%';

                // Update total
                updateTotalGrade();

                // Log success
                console.log(`✅ Saved ${componentType}: ${score}/${maxPoints} for ${studentId}`);
                
                // Add to test results
                addTestResult(`✅ ${componentType} score saved: ${score}/${maxPoints}`, 'success');

            } catch (error) {
                console.error('Error saving component score:', error);
                addTestResult(`❌ Error saving ${componentType}: ${error.message}`, 'error');
            }
        }

        function updateTotalGrade() {
            const tests = parseFloat(document.getElementById('testsScore').value) || 0;
            const quizzes = parseFloat(document.getElementById('quizzesScore').value) || 0;
            const assignments = parseFloat(document.getElementById('assignmentsScore').value) || 0;
            const participation = parseFloat(document.getElementById('participationScore').value) || 0;
            
            const total = tests + quizzes + assignments + participation;
            const letterGrade = getLetterGrade ? getLetterGrade(total) : 'N/A';
            
            document.getElementById('totalGrade').textContent = `${total}/100 (${letterGrade})`;
            
            const totalProgress = document.getElementById('totalProgress');
            totalProgress.style.width = Math.min(total, 100) + '%';
            totalProgress.textContent = total + '%';
        }

        function loadGrades() {
            const studentId = document.getElementById('testStudent').value;
            const subject = document.getElementById('testSubject').value;
            const period = document.getElementById('testPeriod').value;

            if (!studentId || !subject || !period) {
                alert('Please select student, subject, and period first.');
                return;
            }

            try {
                const breakdown = calculateStudentPeriodBreakdown(studentId, subject, period);
                
                document.getElementById('testsScore').value = breakdown.tests || 0;
                document.getElementById('quizzesScore').value = breakdown.quizzes || 0;
                document.getElementById('assignmentsScore').value = breakdown.assignments || 0;
                document.getElementById('participationScore').value = breakdown.participation || 0;

                // Update progress bars
                updateProgressBar('testsProgress', breakdown.tests, 40);
                updateProgressBar('quizzesProgress', breakdown.quizzes, 40);
                updateProgressBar('assignmentsProgress', breakdown.assignments, 10);
                updateProgressBar('participationProgress', breakdown.participation, 10);
                
                updateTotalGrade();
                
                addTestResult(`✅ Loaded grades for ${studentId} - ${subject} - ${period}`, 'success');
                
            } catch (error) {
                console.error('Error loading grades:', error);
                addTestResult(`❌ Error loading grades: ${error.message}`, 'error');
            }
        }

        function updateProgressBar(progressId, value, maxValue) {
            const percentage = (value / maxValue) * 100;
            const progressBar = document.getElementById(progressId);
            progressBar.style.width = percentage + '%';
            progressBar.textContent = Math.round(percentage) + '%';
        }

        function testAllComponents() {
            const studentId = document.getElementById('testStudent').value;
            const subject = document.getElementById('testSubject').value;
            const period = document.getElementById('testPeriod').value;

            if (!studentId || !subject || !period) {
                alert('Please select student, subject, and period first.');
                return;
            }

            // Set test values
            document.getElementById('testsScore').value = 35;
            document.getElementById('quizzesScore').value = 38;
            document.getElementById('assignmentsScore').value = 8;
            document.getElementById('participationScore').value = 9;

            // Save all components
            saveTestScore();
            setTimeout(() => saveQuizScore(), 100);
            setTimeout(() => saveAssignmentScore(), 200);
            setTimeout(() => saveParticipationScore(), 300);
            
            setTimeout(() => {
                addTestResult('✅ All components tested with sample values', 'success');
            }, 500);
        }

        function resetTestGrades() {
            document.getElementById('testsScore').value = 0;
            document.getElementById('quizzesScore').value = 0;
            document.getElementById('assignmentsScore').value = 0;
            document.getElementById('participationScore').value = 0;
            
            // Reset progress bars
            ['testsProgress', 'quizzesProgress', 'assignmentsProgress', 'participationProgress', 'totalProgress'].forEach(id => {
                const bar = document.getElementById(id);
                bar.style.width = '0%';
                bar.textContent = '0%';
            });
            
            document.getElementById('totalGrade').textContent = '0/100 (F)';
            
            addTestResult('🔄 Test grades reset to zero', 'warning');
        }

        function addTestResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultElement = document.createElement('div');
            resultElement.className = `test-result ${type}`;
            resultElement.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            
            if (resultsDiv.innerHTML.includes('Run tests to see results')) {
                resultsDiv.innerHTML = '';
            }
            
            resultsDiv.appendChild(resultElement);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function inspectPeriodGrades() {
            const dataDiv = document.getElementById('dataInspection');
            const displayDiv = document.getElementById('dataDisplay');
            
            if (typeof schoolData !== 'undefined' && schoolData.periodGrades) {
                displayDiv.textContent = JSON.stringify(schoolData.periodGrades, null, 2);
                dataDiv.style.display = 'block';
            } else {
                displayDiv.textContent = 'No period grades data found.';
                dataDiv.style.display = 'block';
            }
        }

        function inspectSchoolData() {
            const dataDiv = document.getElementById('dataInspection');
            const displayDiv = document.getElementById('dataDisplay');
            
            if (typeof schoolData !== 'undefined') {
                const summary = {
                    students: schoolData.students ? schoolData.students.length : 0,
                    grades: schoolData.grades ? schoolData.grades.length : 0,
                    periodGrades: schoolData.periodGrades ? schoolData.periodGrades.length : 0,
                    assessments: schoolData.assessments ? schoolData.assessments.length : 0
                };
                displayDiv.textContent = JSON.stringify(summary, null, 2);
                dataDiv.style.display = 'block';
            } else {
                displayDiv.textContent = 'schoolData object not found.';
                dataDiv.style.display = 'block';
            }
        }

        function clearAllGrades() {
            if (confirm('Are you sure you want to clear all period grades? This action cannot be undone.')) {
                if (typeof schoolData !== 'undefined') {
                    schoolData.periodGrades = [];
                    saveData();
                    addTestResult('🗑️ All period grades cleared', 'warning');
                    resetTestGrades();
                }
            }
        }
    </script>
</body>
</html>
