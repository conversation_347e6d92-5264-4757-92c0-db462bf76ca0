# Bridge of Hope Girls' School Management System - Offline Setup

## Problem
The system fails on computers without internet connection because it depends on external CDN resources:
- Bootstrap CSS & JS
- Font Awesome icons
- jsPDF library
- html2canvas library
- Chart.js library

## Solution: Local Dependencies Setup

### Step 1: Create Local Assets Folder Structure
Create the following folder structure in your project directory:

```
SCHOOL MANAGEMENT SYSTEM/
├── assets/
│   ├── css/
│   ├── js/
│   └── fonts/
├── index.html
├── script.js
├── auth.js
├── backup.js
└── (other files)
```

### Step 2: Download Required Files

#### Bootstrap 5.1.3
1. Download Bootstrap CSS: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css
   - Save as: `assets/css/bootstrap.min.css`

2. Download Bootstrap JS: https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js
   - Save as: `assets/js/bootstrap.bundle.min.js`

#### Font Awesome 6.0.0
1. Download Font Awesome CSS: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css
   - Save as: `assets/css/all.min.css`

2. Download Font Awesome Fonts:
   - https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2
   - https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-regular-400.woff2
   - https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-brands-400.woff2
   - Save all in: `assets/fonts/`

#### JavaScript Libraries
1. Download jsPDF: https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js
   - Save as: `assets/js/jspdf.umd.min.js`

2. Download html2canvas: https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js
   - Save as: `assets/js/html2canvas.min.js`

3. Download Chart.js: https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js
   - Save as: `assets/js/chart.min.js`

### Step 3: Update HTML File
Replace the CDN links in index.html with local file references.

### Step 4: Fix Font Awesome CSS
Edit the downloaded `assets/css/all.min.css` file to update font paths from CDN to local.

## Quick Setup Script
I'll provide updated HTML file and a batch script to help with the setup process.
