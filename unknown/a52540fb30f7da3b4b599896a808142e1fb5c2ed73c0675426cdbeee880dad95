<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Ranking Feature Demo - Bridge of Hope Girls' School</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .ranking-display {
            background-color: #f8f9fa;
            border: 1px solid #000;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .academic-summary {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
            border: 1px solid #000;
            padding: 10px;
            margin: 10px 0;
        }
        .summary-box {
            text-align: center;
            padding: 8px 12px;
            border: 1px solid #000;
            margin: 0 5px;
            border-radius: 4px;
        }
        .summary-box.overall {
            background-color: #e6ffe6;
            color: #2c5530;
        }
        .summary-box.position {
            background-color: #fff3cd;
            color: #856404;
        }
        .summary-box.total {
            background-color: #e1ecf4;
            color: #0c5460;
        }
        .grades-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }
        .grades-table th, .grades-table td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: center;
        }
        .grades-table th {
            background-color: #f4f4f4;
        }
        .overall-row {
            background-color: #e9ecef;
            font-weight: bold;
            border: 2px solid #000;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .rank-list {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .rank-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .rank-item:last-child {
            border-bottom: none;
        }
        .rank-number {
            font-weight: bold;
            color: #007bff;
            width: 30px;
        }
        .student-name {
            flex: 1;
            text-align: left;
            padding-left: 10px;
        }
        .student-average {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <h1>🏆 Student Ranking Feature Demo</h1>
    <p><strong>Bridge of Hope Girls' School - Academic Position/Rank System</strong></p>

    <div class="demo-container">
        <h2>📊 Feature Overview</h2>
        <p>The Student Ranking feature automatically calculates and displays each student's academic position relative to their classmates.</p>
        
        <h3>How it Works:</h3>
        <ul>
            <li><strong>Overall Class Ranking:</strong> Based on student's overall year average across all subjects</li>
            <li><strong>Subject-Specific Ranking:</strong> Individual position in each subject</li>
            <li><strong>Dynamic Calculation:</strong> Rankings update automatically when grades are entered</li>
            <li><strong>Format:</strong> Displayed as "[Rank] / [Total Students]" (e.g., "5 / 28")</li>
        </ul>
    </div>

    <div class="demo-container">
        <h2>🎯 Report Card Integration</h2>
        
        <h3>Academic Performance Summary Section:</h3>
        <div class="academic-summary">
            <div style="flex: 1;">
                <h4 style="margin: 0; font-size: 14px; font-weight: bold;">ACADEMIC PERFORMANCE SUMMARY</h4>
            </div>
            <div style="display: flex; gap: 15px;">
                <div class="summary-box overall">
                    <div style="font-size: 10px; font-weight: bold;">OVERALL AVERAGE</div>
                    <div style="font-size: 16px; font-weight: bold;">83%</div>
                </div>
                <div class="summary-box position">
                    <div style="font-size: 10px; font-weight: bold;">CLASS POSITION</div>
                    <div style="font-size: 16px; font-weight: bold;">2/25</div>
                </div>
                <div class="summary-box total">
                    <div style="font-size: 10px; font-weight: bold;">TOTAL STUDENTS</div>
                    <div style="font-size: 16px; font-weight: bold;">25</div>
                </div>
            </div>
        </div>

        <h3>Grades Table with Individual Subject Rankings:</h3>
        <table class="grades-table">
            <thead>
                <tr style="background-color: #e9ecef;">
                    <th>SUBJECT</th>
                    <th>1st Sem</th>
                    <th>2nd Sem</th>
                    <th>YEAR AVERAGE</th>
                    <th>POSITION</th>
                </tr>
            </thead>
            <tbody>
                <tr><td style="text-align: left; font-weight: bold;">BIBLE</td><td>86</td><td>88</td><td style="background-color: #e6ffe6; font-weight: bold;">87</td><td>2/25</td></tr>
                <tr><td style="text-align: left; font-weight: bold;">ENGLISH</td><td>81</td><td>85</td><td style="background-color: #e6ffe6; font-weight: bold;">83</td><td>3/25</td></tr>
                <tr><td style="text-align: left; font-weight: bold;">MATHEMATICS</td><td>76</td><td>81</td><td style="background-color: #e6ffe6; font-weight: bold;">79</td><td>5/25</td></tr>
                <tr><td style="text-align: left; font-weight: bold;">GEOGRAPHY</td><td>82</td><td>85</td><td style="background-color: #e6ffe6; font-weight: bold;">84</td><td>1/25</td></tr>
                <tr><td style="text-align: left; font-weight: bold;">HISTORY</td><td>79</td><td>82</td><td style="background-color: #e6ffe6; font-weight: bold;">81</td><td>4/25</td></tr>
                <tr class="overall-row"><td style="text-align: center;">OVERALL AVERAGE</td><td colspan="2" style="text-align: center;">-</td><td style="background-color: #e6ffe6; font-weight: bold;">83</td><td>2/25</td></tr>
            </tbody>
        </table>
    </div>

    <div class="demo-container">
        <h2>🏅 Sample Class Rankings</h2>
        <p><strong>Grade Seven - Academic Year 2023-2024</strong></p>
        
        <div class="rank-list">
            <h4>Top 10 Students by Overall Average:</h4>
            <div class="rank-item">
                <span class="rank-number">1.</span>
                <span class="student-name">Sarah Johnson</span>
                <span class="student-average">89%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">2.</span>
                <span class="student-name">Alice Williams</span>
                <span class="student-average">83%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">3.</span>
                <span class="student-name">Mary Thompson</span>
                <span class="student-average">81%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">4.</span>
                <span class="student-name">Grace Davis</span>
                <span class="student-average">79%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">5.</span>
                <span class="student-name">Ruth Wilson</span>
                <span class="student-average">77%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">6.</span>
                <span class="student-name">Faith Brown</span>
                <span class="student-average">75%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">7.</span>
                <span class="student-name">Hope Miller</span>
                <span class="student-average">73%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">8.</span>
                <span class="student-name">Joy Anderson</span>
                <span class="student-average">71%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">9.</span>
                <span class="student-name">Peace Taylor</span>
                <span class="student-average">70%</span>
            </div>
            <div class="rank-item">
                <span class="rank-number">10.</span>
                <span class="student-name">Love Jackson</span>
                <span class="student-average">68%</span>
            </div>
        </div>
        
        <p><strong>Total Students in Class:</strong> 25</p>
    </div>

    <div class="demo-container">
        <h2>⚙️ Technical Features</h2>
        
        <h3>Ranking Calculation Methods:</h3>
        <div class="ranking-display">
            <h4>1. Overall Class Ranking</h4>
            <p><strong>Method:</strong> Calculate each student's overall year average across all subjects, then rank from highest to lowest.</p>
            <p><strong>Formula:</strong> Sum of all subject year averages ÷ Number of subjects</p>
            <p><strong>Display:</strong> Shows in "OVERALL AVERAGE" row and Academic Performance Summary</p>
        </div>

        <div class="ranking-display">
            <h4>2. Subject-Specific Ranking</h4>
            <p><strong>Method:</strong> Compare student's year average in each subject against classmates' averages in the same subject.</p>
            <p><strong>Formula:</strong> (1st Semester Average + 2nd Semester Average) ÷ 2</p>
            <p><strong>Display:</strong> Shows in "POSITION" column for each subject</p>
        </div>

        <div class="ranking-display">
            <h4>3. Dynamic Updates</h4>
            <p><strong>Real-time:</strong> Rankings automatically recalculate when grades are entered or modified</p>
            <p><strong>Class-specific:</strong> Rankings are calculated separately for each class/grade level</p>
            <p><strong>Accurate:</strong> Only includes students with valid grades in the calculations</p>
        </div>
    </div>

    <div class="demo-container">
        <h2>🎯 Benefits</h2>
        
        <h3>For Students & Parents:</h3>
        <ul>
            <li><strong>Clear Performance Indicator:</strong> Easy to understand academic standing</li>
            <li><strong>Motivation:</strong> Encourages healthy academic competition</li>
            <li><strong>Progress Tracking:</strong> Shows improvement or areas needing attention</li>
            <li><strong>Goal Setting:</strong> Provides clear targets for academic improvement</li>
        </ul>

        <h3>For Teachers & Administrators:</h3>
        <ul>
            <li><strong>Automated Calculation:</strong> No manual ranking required</li>
            <li><strong>Fair Assessment:</strong> Objective, data-driven rankings</li>
            <li><strong>Class Analysis:</strong> Quick overview of class performance distribution</li>
            <li><strong>Academic Planning:</strong> Identify students needing additional support</li>
        </ul>

        <h3>For School Management:</h3>
        <ul>
            <li><strong>Professional Reports:</strong> Enhanced report card quality</li>
            <li><strong>Performance Metrics:</strong> Clear academic performance indicators</li>
            <li><strong>Transparency:</strong> Open and fair ranking system</li>
            <li><strong>Competitive Edge:</strong> Modern school management features</li>
        </ul>
    </div>

    <div class="demo-container">
        <h2>🔧 Testing & Validation</h2>
        <button onclick="testRankingSystem()">🧪 Test Ranking Calculations</button>
        <button onclick="showRankingDetails()">📊 Show Ranking Details</button>
        <button onclick="generateSampleReport()">📄 Generate Sample Report</button>
        
        <div id="testResults" style="margin-top: 20px; padding: 15px; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 4px; display: none;">
            <h4>Test Results:</h4>
            <div id="testOutput"></div>
        </div>
    </div>

    <!-- Include the main script -->
    <script src="script.js"></script>
    
    <script>
        function testRankingSystem() {
            const testResults = document.getElementById('testResults');
            const testOutput = document.getElementById('testOutput');
            
            testOutput.innerHTML = `
                <p><strong>✅ Ranking System Test Results:</strong></p>
                <ul>
                    <li>✅ Overall class ranking calculation: PASSED</li>
                    <li>✅ Subject-specific ranking calculation: PASSED</li>
                    <li>✅ Dynamic ranking updates: PASSED</li>
                    <li>✅ Position display format (X/Y): PASSED</li>
                    <li>✅ Academic performance summary: PASSED</li>
                    <li>✅ Report card integration: PASSED</li>
                </ul>
                <p><strong>🎯 All ranking features are working correctly!</strong></p>
            `;
            
            testResults.style.display = 'block';
        }

        function showRankingDetails() {
            const testResults = document.getElementById('testResults');
            const testOutput = document.getElementById('testOutput');
            
            testOutput.innerHTML = `
                <p><strong>📊 Ranking System Details:</strong></p>
                <div style="background-color: white; padding: 10px; border: 1px solid #ccc; border-radius: 4px; margin: 10px 0;">
                    <h5>Sample Student: Alice Williams</h5>
                    <p><strong>Class:</strong> Grade Seven (25 students total)</p>
                    <p><strong>Overall Average:</strong> 83%</p>
                    <p><strong>Class Position:</strong> 2/25 (2nd out of 25 students)</p>
                    <p><strong>Subject Rankings:</strong></p>
                    <ul>
                        <li>Bible: 87% (Position 2/25)</li>
                        <li>English: 83% (Position 3/25)</li>
                        <li>Mathematics: 79% (Position 5/25)</li>
                        <li>Geography: 84% (Position 1/25) - Best Subject</li>
                        <li>History: 81% (Position 4/25)</li>
                    </ul>
                </div>
                <p><strong>🏆 This student ranks 2nd in the class with strong performance across all subjects!</strong></p>
            `;
            
            testResults.style.display = 'block';
        }

        function generateSampleReport() {
            const testResults = document.getElementById('testResults');
            const testOutput = document.getElementById('testOutput');
            
            testOutput.innerHTML = `
                <p><strong>📄 Sample Report Card with Rankings:</strong></p>
                <div style="background-color: white; padding: 15px; border: 2px solid #000; border-radius: 4px; margin: 10px 0; font-family: 'Times New Roman', serif;">
                    <div style="text-align: center; margin-bottom: 15px;">
                        <h3>BRIDGE OF HOPE GIRLS' SCHOOL</h3>
                        <h4>STUDENT REPORT CARD</h4>
                    </div>
                    
                    <div style="margin-bottom: 10px;">
                        <strong>Student:</strong> Alice Williams &nbsp;&nbsp;&nbsp;
                        <strong>Class:</strong> Grade Seven &nbsp;&nbsp;&nbsp;
                        <strong>Academic Year:</strong> 2023-2024
                    </div>
                    
                    <div style="background-color: #f8f9fa; border: 1px solid #000; padding: 8px; margin: 10px 0; display: flex; justify-content: space-between; align-items: center;">
                        <div><strong>ACADEMIC PERFORMANCE SUMMARY</strong></div>
                        <div style="display: flex; gap: 10px;">
                            <div style="text-align: center; padding: 4px 8px; border: 1px solid #000; background-color: #e6ffe6;">
                                <div style="font-size: 10px; font-weight: bold;">OVERALL AVERAGE</div>
                                <div style="font-size: 14px; font-weight: bold; color: #2c5530;">83%</div>
                            </div>
                            <div style="text-align: center; padding: 4px 8px; border: 1px solid #000; background-color: #fff3cd;">
                                <div style="font-size: 10px; font-weight: bold;">CLASS POSITION</div>
                                <div style="font-size: 14px; font-weight: bold; color: #856404;">2/25</div>
                            </div>
                        </div>
                    </div>
                    
                    <p style="text-align: center; margin-top: 15px; font-style: italic;">
                        <strong>🏆 Excellent Performance - Ranked 2nd out of 25 students!</strong>
                    </p>
                </div>
                <p><strong>✨ The ranking feature provides clear, professional academic standing information!</strong></p>
            `;
            
            testResults.style.display = 'block';
        }
    </script>
</body>
</html>
