<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Management System - Portable Launcher</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5rem;
        }
        
        .header p {
            color: #7f8c8d;
            font-size: 1.1rem;
        }
        
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        
        .warning h3 {
            color: #856404;
            margin-top: 0;
        }
        
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
        }
        
        .info h3 {
            color: #0c5460;
            margin-top: 0;
        }
        
        .launch-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .launch-card {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .launch-card:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,123,255,0.1);
        }
        
        .launch-card.recommended {
            border-color: #28a745;
            background-color: #f8fff9;
        }
        
        .launch-card h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .launch-card p {
            color: #6c757d;
            margin-bottom: 20px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #545b62;
            transform: translateY(-2px);
        }
        
        .status-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .status-info h4 {
            color: #495057;
            margin-bottom: 15px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .status-good {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .container {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .launch-options {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏫 School Management System</h1>
            <p>Portable Edition - Run from anywhere without installation</p>
        </div>
        
        <div class="warning">
            <h3>⚠️ Important Setup Instructions</h3>
            <p><strong>For best results when running from USB drive:</strong></p>
            <ol>
                <li><strong>Firefox Browser:</strong> Works best with direct file access</li>
                <li><strong>Chrome/Edge:</strong> May require local server for data persistence</li>
                <li><strong>Data Backup:</strong> Always export your data regularly using the backup feature</li>
                <li><strong>Same Browser:</strong> Always use the same browser and method to maintain your data</li>
            </ol>
        </div>
        
        <div class="info">
            <h3>📋 System Requirements</h3>
            <ul>
                <li>Modern web browser (Firefox, Chrome, Edge, Safari)</li>
                <li>JavaScript enabled</li>
                <li>No internet connection required (works offline)</li>
                <li>Minimum 50MB free space for data storage</li>
            </ul>
        </div>
        
        <div class="launch-options">
            <div class="launch-card recommended">
                <h4>🚀 Direct Launch (Recommended)</h4>
                <p>Launch directly from your browser. Works best with Firefox. Chrome may have storage limitations.</p>
                <a href="login.html" class="btn btn-success">Launch System</a>
            </div>
            
            <div class="launch-card">
                <h4>🌐 Local Server Mode</h4>
                <p>Run with a local web server for maximum compatibility. Best for Chrome/Edge users.</p>
                <button onclick="startLocalServer()" class="btn btn-primary">Start Server</button>
            </div>
            
            <div class="launch-card">
                <h4>📖 View Documentation</h4>
                <p>Read the complete user guide and troubleshooting information.</p>
                <a href="README.txt" class="btn btn-secondary">Open README</a>
                <a href="troubleshooting.html" class="btn btn-secondary">🔧 Troubleshooting</a>
                <a href="BRIDGE_OF_HOPE_GRADE_STRUCTURE.md" class="btn btn-secondary">📊 Grade Structure</a>
            </div>
        </div>
        
        <div class="status-info">
            <h4>🔍 System Status Check</h4>
            <div id="statusContainer">
                <div class="status-item">
                    <span>Browser:</span>
                    <span id="browserInfo">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Local Storage:</span>
                    <span id="localStorageStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Session Storage:</span>
                    <span id="sessionStorageStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Cookies:</span>
                    <span id="cookieStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span>Protocol:</span>
                    <span id="protocolInfo">Checking...</span>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 School Management System - Portable Edition</p>
            <p>For technical support: <strong><EMAIL></strong></p>
        </div>
    </div>

    <script>
        // Check system compatibility
        function checkSystemStatus() {
            // Browser info
            const browserInfo = navigator.userAgent.includes('Firefox') ? 'Firefox (Excellent)' :
                               navigator.userAgent.includes('Chrome') ? 'Chrome (Good)' :
                               navigator.userAgent.includes('Edge') ? 'Edge (Good)' :
                               navigator.userAgent.includes('Safari') ? 'Safari (Good)' : 'Unknown';
            document.getElementById('browserInfo').textContent = browserInfo;
            
            // Local storage check
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                document.getElementById('localStorageStatus').innerHTML = '<span class="status-good">✓ Available</span>';
            } catch (e) {
                document.getElementById('localStorageStatus').innerHTML = '<span class="status-error">✗ Blocked</span>';
            }
            
            // Session storage check
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                document.getElementById('sessionStorageStatus').innerHTML = '<span class="status-good">✓ Available</span>';
            } catch (e) {
                document.getElementById('sessionStorageStatus').innerHTML = '<span class="status-error">✗ Blocked</span>';
            }
            
            // Cookie check
            const cookieStatus = navigator.cookieEnabled ? 
                '<span class="status-good">✓ Enabled</span>' : 
                '<span class="status-warning">⚠ Disabled</span>';
            document.getElementById('cookieStatus').innerHTML = cookieStatus;
            
            // Protocol check
            const protocol = window.location.protocol;
            const protocolStatus = protocol === 'file:' ? 
                '<span class="status-warning">⚠ File Protocol</span>' : 
                '<span class="status-good">✓ HTTP Protocol</span>';
            document.getElementById('protocolInfo').innerHTML = protocolStatus;
        }
        
        function startLocalServer() {
            alert('To start the local server:\n\n1. Open Command Prompt/Terminal in this folder\n2. Run: python -m http.server 8000\n3. Open: http://localhost:8000\n\nOr use the start_server.bat file if available.');
        }
        
        // Run status check when page loads
        document.addEventListener('DOMContentLoaded', checkSystemStatus);
    </script>
</body>
</html>
