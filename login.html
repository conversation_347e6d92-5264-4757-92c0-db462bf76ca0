<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - School Grade Management System</title>

    <!-- Bootstrap CSS - with offline fallback -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet"
          onerror="this.onerror=null;this.href='./libs/bootstrap.min.css';">

    <!-- Font Awesome - with offline fallback -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
          onerror="this.onerror=null;this.href='./libs/fontawesome.min.css';">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="./style.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 20px;
        }
        
        .login-card {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .login-header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #eee;
        }
        
        .login-header h3 {
            margin-bottom: 0;
            color: var(--primary-color);
        }
        
        .login-body {
            padding: 30px;
        }
        
        .login-footer {
            text-align: center;
            padding: 15px;
            border-top: 1px solid #eee;
            font-size: 0.9rem;
            color: #6c757d;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .btn-login {
            width: 100%;
            padding: 12px;
            font-weight: 500;
        }
        
        .school-logo {
            max-width: 100px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="card login-card">
            <div class="login-header">
                <img src="https://via.placeholder.com/100" alt="School Logo" class="school-logo">
                <h3>School Management System</h3>
                <p class="text-muted">Sign in to access your account</p>
            </div>
            <div class="login-body">
                <div id="loginAlert" class="alert alert-danger d-none" role="alert">
                    Invalid username or password!
                </div>
                
                <form id="loginForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" placeholder="Username" required>
                        <label for="username">Username</label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" placeholder="Password" required>
                        <label for="password">Password</label>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Remember me
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login">
                        <i class="fas fa-sign-in-alt me-2"></i> Sign In
                    </button>
                </form>
            </div>
            <div class="login-footer">
                &copy; 2023 School Management System. All rights reserved.
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper - with offline fallback -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"
            onerror="document.write('<script src=\'./libs/bootstrap.bundle.min.js\'><\/script>')"></script>

    <!-- Storage Compatibility Layer -->
    <script src="./storage-compat.js"></script>

    <!-- Login JS -->
    <script src="./login.js"></script>
</body>
</html>
