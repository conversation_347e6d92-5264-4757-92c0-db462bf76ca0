from dataclasses import dataclass
from typing import List, Dict, Optional

class ReportCard:
    SUBJECTS = [
        "BIBLE",
        "ENGLISH",
        "LITERATURE",
        "MATHEMATICS",
        "GEOGRAPHY",
        "HISTORY",
        "CIVICS",
        "GENERAL SCIENCE",
        "CONFLICT MANAGEMENT",
        "HOME ECONOMICS",
        "COMPUTER",
        "FRENC<PERSON>",
        "PHYSICAL EDUCATION"
    ]
    
    PERIODS = [
        "1st Pd", "2nd Pd", "3rd Pd",
        "Exam 1", "AVERAGE (1st Semester)",
        "4th Pd", "5th Pd", "6th Pd",
        "Exam 2", "AVERAGE (2nd Semester)",
        "YEAR AVERAGE", "POSITION"
    ]

    @dataclass
class StudentGrades:
        student_id: str
        student_name: str
        grades: Dict[str, List[float]]  # {subject: [1st Pd, 2nd Pd, 3rd Pd, Exam 1, ...]}
        
    def __init__(self):
        self.students = []

    def add_student_grades(self, student_id: str, student_name: str, grades: Dict[str, List[float]]):
        """Add or update a student's grades"""
        student = StudentGrades(student_id, student_name, grades)
        self.students.append(student)
        
    def calculate_averages(self):
        """Calculate all averages for each student"""
        for student in self.students:
            for subject in student.grades:
                # Calculate Semester 1 Average
                semester_1_grades = student.grades[subject][:4]
                student.grades[subject][4] = sum(semester_1_grades) / 4
                
                # Calculate Semester 2 Average
                semester_2_grades = student.grades[subject][5:9]
                student.grades[subject][9] = sum(semester_2_grades) / 4
                
                # Calculate Year Average
                semester_averages = [student.grades[subject][4], student.grades[subject][9]]
                student.grades[subject][10] = sum(semester_averages) / 2

    def get_report_card(self, student_id: str) -> Optional[StudentGrades]:
        """Get formatted report card for a specific student"""
        for student in self.students:
            if student.student_id == student_id:
                return student
        return None

    def generate_html_report(self, student_id: str) -> str:
        """Generate HTML representation of the report card"""
        student = self.get_report_card(student_id)
        if not student:
            return "Student not found"
            
        html = """
        <html>
        <head>
            <title>Bridge of Hope Girls' School Report Card</title>
            <style>
                body { font-family: Arial, sans-serif; }
                .report-card { width: 100%; border-collapse: collapse; }
                .report-card th, .report-card td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                .report-card th { background-color: #f4f4f4; }
            </style>
        </head>
        <body>
            <h1>Bridge of Hope Girls' School</h1>
            <h2>Report Card</h2>
            <p>Student: {}</p>
            <table class="report-card">
                <thead>
                    <tr>
                        <th>Subject</th>
                        <th>1st Pd</th>
                        <th>2nd Pd</th>
                        <th>3rd Pd</th>
                        <th>Exam 1</th>
                        <th>Average (1st Sem)</th>
                        <th>4th Pd</th>
                        <th>5th Pd</th>
                        <th>6th Pd</th>
                        <th>Exam 2</th>
                        <th>Average (2nd Sem)</th>
                        <th>Year Average</th>
                        <th>Position</th>
                    </tr>
                </thead>
                <tbody>
        """.format(student.student_name)
        
        for subject in self.SUBJECTS:
            grades = student.grades.get(subject, [0] * 12)
            html += """
                <tr>
                    <td>{}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{:.2f}</td>
                    <td>{}</td>
                </tr>
            """.format(subject, *grades)
            
        html += """
                </tbody>
            </table>
        </body>
        </html>
        """
        return html
