<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Troubleshooting Guide - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        
        .problem {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .solution {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .btn-danger {
            background-color: #dc3545;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn-success {
            background-color: #28a745;
        }
        
        .btn-success:hover {
            background-color: #1e7e34;
        }
        
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        
        .alert-info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background-color: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Troubleshooting Guide</h1>
            <p>School Management System - Common Issues and Solutions</p>
        </div>

        <div class="problem">
            <h3>❌ Problem: "Failed to enter grade. Error: grades.filter is not a function"</h3>
            <p><strong>Cause:</strong> The grades data structure has been corrupted or is not properly initialized as an array.</p>
        </div>

        <div class="problem">
            <h3>📊 Problem: Report Card Table Structure Issue</h3>
            <p><strong>Symptoms:</strong> Report cards showing subjects in a list format instead of proper table structure.</p>
            <p><strong>Example of the issue:</strong></p>
            <div class="code">
                BIBLE 60 - - - 60 - - - - - - -<br>
                ENGLISH 88 - - - 88 - - - - - - -<br>
                PHONICS - - - - - - - - - - - -
            </div>
            <p><strong>Expected format:</strong> Subjects should appear as rows in a proper table with columns for each grading period.</p>
        </div>

        <div class="solution">
            <h4>✅ Solution 1: Automatic Fix (Recommended)</h4>
            <p>The system now automatically detects and fixes this issue. Simply refresh the page and the system will repair the data structure.</p>
            <button class="btn" onclick="window.location.reload()">🔄 Refresh Page</button>
        </div>

        <div class="solution">
            <h4>✅ Solution 2: Manual Fix Using Browser Console</h4>
            <p>If the automatic fix doesn't work, you can manually repair the data:</p>
            <ol>
                <li>Press <strong>F12</strong> to open browser developer tools</li>
                <li>Click on the <strong>Console</strong> tab</li>
                <li>Type the following commands and press Enter after each:</li>
            </ol>
            
            <div class="code">
                checkDataIntegrity()
            </div>
            <p>This will check if there are any data structure issues.</p>
            
            <div class="code">
                fixDataStructures()
            </div>
            <p>This will automatically fix any detected issues.</p>

            <div class="code">
                testGradeEntry()
            </div>
            <p>This will test if grade entry functionality is working properly.</p>

            <div class="code">
                generateBridgeOfHopeSampleData()
            </div>
            <p>This will generate sample student data matching Bridge of Hope format for testing.</p>

            <div class="code">
                testReportCardGeneration()
            </div>
            <p>This will test if report card generation is working properly and show structure details.</p>

            <div class="code">
                debugGradesStructure()
            </div>
            <p>This will show detailed information about grades structure for debugging.</p>

            <div class="code">
                testReportsSection()
            </div>
            <p>This will test the entire Reports section including report card generation and table structure.</p>
        </div>

        <div class="solution">
            <h4>📊 Solution for Report Card Table Structure Issue</h4>
            <p>If report cards are showing subjects in a list format instead of a proper table:</p>
            <ol>
                <li>Open browser console (F12) and run: <code>testReportsSection()</code></li>
                <li>If the test shows table structure issues, run: <code>generateBridgeOfHopeSampleData()</code></li>
                <li>Then try generating report cards again from the Reports section</li>
                <li>If the issue persists, refresh the page and try again</li>
            </ol>
            <p><strong>Note:</strong> Make sure you have students with grades in the selected class before generating report cards.</p>
        </div>

        <div class="solution">
            <h4>✅ Solution 3: Reset Data (Last Resort)</h4>
            <p><strong>⚠️ Warning:</strong> This will delete all your data. Only use if other solutions don't work.</p>
            <button class="btn btn-danger" onclick="resetAllData()">🗑️ Reset All Data</button>
        </div>

        <div class="alert alert-info">
            <h4>📋 Other Common Issues</h4>
            <ul>
                <li><strong>Students not appearing in dropdowns:</strong> Make sure students are added to the correct class</li>
                <li><strong>Subjects not showing:</strong> Check that subjects are configured for the selected class</li>
                <li><strong>Data not saving:</strong> Check browser storage permissions and try server mode</li>
                <li><strong>Print issues:</strong> Use Chrome or Firefox for best printing results</li>
            </ul>
        </div>

        <div class="alert alert-warning">
            <h4>🛡️ Prevention Tips</h4>
            <ul>
                <li>Always use the backup feature regularly</li>
                <li>Don't clear browser data if you want to keep your information</li>
                <li>Use the same browser and access method consistently</li>
                <li>Keep backup files in a safe location</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn btn-success" onclick="window.location.href='index.html'">🏠 Back to System</button>
            <button class="btn" onclick="runDiagnostics()">🔍 Run Diagnostics</button>
        </div>

        <div id="diagnosticsResult" style="margin-top: 20px;"></div>
    </div>

    <script>
        function resetAllData() {
            if (confirm('⚠️ WARNING: This will delete ALL your data including students, grades, and settings.\n\nAre you absolutely sure you want to continue?')) {
                if (confirm('This action cannot be undone. Click OK to proceed with data reset.')) {
                    try {
                        // Clear all storage
                        localStorage.clear();
                        sessionStorage.clear();
                        
                        // Clear cookies
                        document.cookie.split(";").forEach(function(c) { 
                            document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
                        });
                        
                        alert('✅ All data has been reset. The page will now reload.');
                        window.location.reload();
                    } catch (error) {
                        alert('❌ Error resetting data: ' + error.message);
                    }
                }
            }
        }

        function runDiagnostics() {
            const resultDiv = document.getElementById('diagnosticsResult');
            resultDiv.innerHTML = '<h4>🔍 Running Diagnostics...</h4>';
            
            const diagnostics = [];
            
            // Check browser
            const userAgent = navigator.userAgent;
            if (userAgent.includes('Chrome')) {
                diagnostics.push('✅ Browser: Chrome (Good compatibility)');
            } else if (userAgent.includes('Firefox')) {
                diagnostics.push('✅ Browser: Firefox (Excellent compatibility)');
            } else if (userAgent.includes('Safari')) {
                diagnostics.push('✅ Browser: Safari (Good compatibility)');
            } else {
                diagnostics.push('⚠️ Browser: Unknown (May have compatibility issues)');
            }
            
            // Check storage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                diagnostics.push('✅ Local Storage: Available');
            } catch (e) {
                diagnostics.push('❌ Local Storage: Not available');
            }
            
            try {
                sessionStorage.setItem('test', 'test');
                sessionStorage.removeItem('test');
                diagnostics.push('✅ Session Storage: Available');
            } catch (e) {
                diagnostics.push('❌ Session Storage: Not available');
            }
            
            // Check cookies
            if (navigator.cookieEnabled) {
                diagnostics.push('✅ Cookies: Enabled');
            } else {
                diagnostics.push('❌ Cookies: Disabled');
            }
            
            // Check protocol
            const protocol = window.location.protocol;
            if (protocol === 'https:' || protocol === 'http:') {
                diagnostics.push('✅ Protocol: ' + protocol + ' (Good for data persistence)');
            } else {
                diagnostics.push('⚠️ Protocol: ' + protocol + ' (May have storage limitations)');
            }
            
            // Check JavaScript
            diagnostics.push('✅ JavaScript: Enabled and working');
            
            // Display results
            let html = '<div class="alert alert-info"><h4>📊 Diagnostic Results</h4><ul>';
            diagnostics.forEach(item => {
                html += '<li>' + item + '</li>';
            });
            html += '</ul></div>';
            
            // Add recommendations
            html += '<div class="alert alert-warning"><h4>💡 Recommendations</h4>';
            if (protocol === 'file:') {
                html += '<p>• Consider using Firefox for better file:// protocol support</p>';
                html += '<p>• Or use the local server mode for maximum compatibility</p>';
            }
            if (!navigator.cookieEnabled) {
                html += '<p>• Enable cookies for better data persistence</p>';
            }
            html += '<p>• Always backup your data regularly</p>';
            html += '<p>• Use the same browser and access method consistently</p>';
            html += '</div>';
            
            resultDiv.innerHTML = html;
        }
    </script>
</body>
</html>
