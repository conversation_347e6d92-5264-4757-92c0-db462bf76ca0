<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Test - School Management System</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        
        .test-pass {
            background-color: #d4edda;
            color: #155724;
        }
        
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .test-warning {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 3px;
        }
        
        .pass {
            background-color: #28a745;
            color: white;
        }
        
        .fail {
            background-color: #dc3545;
            color: white;
        }
        
        .warning {
            background-color: #ffc107;
            color: black;
        }
        
        .info {
            background-color: #17a2b8;
            color: white;
        }
        
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background-color: #0056b3;
        }
        
        .results {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 School Management System - Compatibility Test</h1>
        <p>This page tests the compatibility and functionality of your system setup.</p>
        
        <div id="testResults">
            <h3>Running Tests...</h3>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <button class="btn" onclick="runTests()">🔄 Run Tests Again</button>
            <button class="btn" onclick="window.location.href='launcher.html'">🚀 Launch System</button>
        </div>
        
        <div class="results">
            <h4>📋 Test Results Summary</h4>
            <div id="summary">Tests not run yet.</div>
        </div>
        
        <div class="results">
            <h4>💡 Recommendations</h4>
            <div id="recommendations">Run tests to see recommendations.</div>
        </div>
    </div>

    <!-- Include storage compatibility layer -->
    <script src="./storage-compat.js"></script>
    
    <script>
        function runTests() {
            const results = document.getElementById('testResults');
            const summary = document.getElementById('summary');
            const recommendations = document.getElementById('recommendations');
            
            results.innerHTML = '<h3>🔍 Running Compatibility Tests...</h3>';
            
            let tests = [];
            let passCount = 0;
            let failCount = 0;
            let warningCount = 0;
            
            // Test 1: JavaScript Support
            try {
                const testVar = 'test';
                tests.push({
                    name: 'JavaScript Support',
                    status: 'pass',
                    message: 'JavaScript is enabled and working'
                });
                passCount++;
            } catch (e) {
                tests.push({
                    name: 'JavaScript Support',
                    status: 'fail',
                    message: 'JavaScript is not working properly'
                });
                failCount++;
            }
            
            // Test 2: Storage Compatibility Layer
            if (window.StorageCompat) {
                tests.push({
                    name: 'Storage Compatibility Layer',
                    status: 'pass',
                    message: 'Storage compatibility layer loaded successfully'
                });
                passCount++;
            } else {
                tests.push({
                    name: 'Storage Compatibility Layer',
                    status: 'fail',
                    message: 'Storage compatibility layer not found'
                });
                failCount++;
            }
            
            // Test 3: Local Storage
            const storageInfo = window.StorageCompat ? window.StorageCompat.getStorageInfo() : null;
            if (storageInfo && storageInfo.localStorage) {
                tests.push({
                    name: 'Local Storage',
                    status: 'pass',
                    message: 'localStorage is available and working'
                });
                passCount++;
            } else {
                tests.push({
                    name: 'Local Storage',
                    status: 'warning',
                    message: 'localStorage not available - using fallback storage'
                });
                warningCount++;
            }
            
            // Test 4: Session Storage
            if (storageInfo && storageInfo.sessionStorage) {
                tests.push({
                    name: 'Session Storage',
                    status: 'pass',
                    message: 'sessionStorage is available'
                });
                passCount++;
            } else {
                tests.push({
                    name: 'Session Storage',
                    status: 'warning',
                    message: 'sessionStorage not available'
                });
                warningCount++;
            }
            
            // Test 5: Cookies
            if (storageInfo && storageInfo.cookieEnabled) {
                tests.push({
                    name: 'Cookie Support',
                    status: 'pass',
                    message: 'Cookies are enabled'
                });
                passCount++;
            } else {
                tests.push({
                    name: 'Cookie Support',
                    status: 'warning',
                    message: 'Cookies are disabled'
                });
                warningCount++;
            }
            
            // Test 6: Protocol Check
            const protocol = window.location.protocol;
            if (protocol === 'http:' || protocol === 'https:') {
                tests.push({
                    name: 'Protocol',
                    status: 'pass',
                    message: `Running on ${protocol} - optimal for data persistence`
                });
                passCount++;
            } else {
                tests.push({
                    name: 'Protocol',
                    status: 'warning',
                    message: `Running on ${protocol} - may have storage limitations`
                });
                warningCount++;
            }
            
            // Test 7: Browser Detection
            const userAgent = navigator.userAgent;
            let browserStatus = 'info';
            let browserMessage = 'Unknown browser';
            
            if (userAgent.includes('Firefox')) {
                browserStatus = 'pass';
                browserMessage = 'Firefox - Excellent compatibility';
                passCount++;
            } else if (userAgent.includes('Chrome')) {
                browserStatus = 'pass';
                browserMessage = 'Chrome - Good compatibility';
                passCount++;
            } else if (userAgent.includes('Edge')) {
                browserStatus = 'pass';
                browserMessage = 'Edge - Good compatibility';
                passCount++;
            } else if (userAgent.includes('Safari')) {
                browserStatus = 'pass';
                browserMessage = 'Safari - Good compatibility';
                passCount++;
            } else {
                browserStatus = 'warning';
                browserMessage = 'Unknown browser - compatibility uncertain';
                warningCount++;
            }
            
            tests.push({
                name: 'Browser Compatibility',
                status: browserStatus,
                message: browserMessage
            });
            
            // Test 8: File Access Test
            try {
                const testData = JSON.stringify({test: 'data'});
                window.StorageCompat.setItem('test_key', testData);
                const retrieved = window.StorageCompat.getItem('test_key');
                
                if (retrieved === testData) {
                    tests.push({
                        name: 'Data Storage Test',
                        status: 'pass',
                        message: 'Data can be stored and retrieved successfully'
                    });
                    passCount++;
                } else {
                    tests.push({
                        name: 'Data Storage Test',
                        status: 'fail',
                        message: 'Data storage/retrieval failed'
                    });
                    failCount++;
                }
                
                // Clean up test data
                window.StorageCompat.removeItem('test_key');
            } catch (e) {
                tests.push({
                    name: 'Data Storage Test',
                    status: 'fail',
                    message: 'Data storage test failed: ' + e.message
                });
                failCount++;
            }
            
            // Display results
            let html = '<h3>🧪 Test Results</h3>';
            tests.forEach(test => {
                const statusClass = test.status === 'pass' ? 'test-pass' : 
                                  test.status === 'fail' ? 'test-fail' : 'test-warning';
                const statusBadge = test.status === 'pass' ? 'pass' : 
                                  test.status === 'fail' ? 'fail' : 
                                  test.status === 'info' ? 'info' : 'warning';
                
                html += `
                    <div class="test-item ${statusClass}">
                        <span><strong>${test.name}:</strong> ${test.message}</span>
                        <span class="status ${statusBadge}">${test.status.toUpperCase()}</span>
                    </div>
                `;
            });
            
            results.innerHTML = html;
            
            // Summary
            const total = passCount + failCount + warningCount;
            summary.innerHTML = `
                <strong>Total Tests:</strong> ${total}<br>
                <strong>✅ Passed:</strong> ${passCount}<br>
                <strong>⚠️ Warnings:</strong> ${warningCount}<br>
                <strong>❌ Failed:</strong> ${failCount}
            `;
            
            // Recommendations
            let recs = [];
            
            if (failCount > 0) {
                recs.push('❌ Some critical tests failed. The system may not work properly.');
            }
            
            if (protocol === 'file:' && !storageInfo.localStorage) {
                recs.push('💡 Consider using Firefox browser for better file:// protocol support.');
                recs.push('🌐 Or use the local server mode for maximum compatibility.');
            }
            
            if (warningCount > 0 && failCount === 0) {
                recs.push('⚠️ Some features may be limited, but the system should work.');
            }
            
            if (passCount === total) {
                recs.push('🎉 All tests passed! Your system is fully compatible.');
            }
            
            recs.push('💾 Remember to backup your data regularly using the system\'s backup feature.');
            recs.push('🔄 Always use the same browser and access method to maintain your data.');
            
            recommendations.innerHTML = recs.map(rec => `<div style="margin: 5px 0;">${rec}</div>`).join('');
        }
        
        // Run tests automatically when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(runTests, 500);
        });
    </script>
</body>
</html>
