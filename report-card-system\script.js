const SUBJECTS = [
    "BIBLE",
    "ENGLISH",
    "LITER<PERSON>UR<PERSON>",
    "MATHEMATICS",
    "GEOGRAPHY",
    "HISTORY",
    "CIVICS",
    "GENERAL SCIENCE",
    "CONFLICT MANAGEMENT",
    "HOME ECONOMICS",
    "COMPUTER",
    "FRENC<PERSON>",
    "PHYSICAL EDUCATION"
];

const PERIODS = ["1st Pd", "2nd Pd", "3rd Pd", "Exam 1"];

// Initialize the grade inputs
function initializeGradeInputs() {
    const container = document.getElementById('gradesContainer');
    container.innerHTML = '';

    SUBJECTS.forEach(subject => {
        const row = document.createElement('div');
        row.className = 'grade-row';
        
        const label = document.createElement('div');
        label.className = 'grade-label';
        label.textContent = subject;
        
        const inputs = document.createElement('div');
        inputs.className = 'grade-inputs';
        
        PERIODS.forEach(period => {
            const input = document.createElement('input');
            input.className = 'grade-input';
            input.type = 'number';
            input.min = 0;
            input.max = 100;
            input.placeholder = period;
            input.dataset.subject = subject;
            input.dataset.period = period;
            inputs.appendChild(input);
        });
        
        row.appendChild(label);
        row.appendChild(inputs);
        container.appendChild(row);
    });
}

// Calculate averages
function calculateAverages(grades) {
    const averages = {};
    
    // Calculate semester 1 average
    const semester1Grades = grades.slice(0, 4);
    averages['AVERAGE (1st Semester)'] = semester1Grades.reduce((acc, curr) => acc + curr, 0) / 4;
    
    // Calculate semester 2 average
    const semester2Grades = grades.slice(4, 8);
    averages['AVERAGE (2nd Semester)'] = semester2Grades.reduce((acc, curr) => acc + curr, 0) / 4;
    
    // Calculate year average
    averages['YEAR AVERAGE'] = (averages['AVERAGE (1st Semester)'] + averages['AVERAGE (2nd Semester)']) / 2;
    
    return averages;
}

// Generate report card HTML
function generateReportCard(studentData) {
    const previewContent = document.getElementById('previewContent');
    previewContent.innerHTML = `
        <div class="report-card">
            <div class="student-info">
                <p><strong>Student Name:</strong> ${studentData.name}</p>
                <p><strong>Class:</strong> ${studentData.class}</p>
                <p><strong>Term:</strong> ${studentData.term}</p>
                <p><strong>Year:</strong> ${studentData.year}</p>
            </div>

            <table class="grades-table">
                <thead>
                    <tr>
                        <th>Subject</th>
                        ${PERIODS.map(period => `<th>${period}</th>`).join('')}
                        <th>AVERAGE (1st Sem)</th>
                        <th>4th Pd</th>
                        <th>5th Pd</th>
                        <th>6th Pd</th>
                        <th>Exam 2</th>
                        <th>AVERAGE (2nd Sem)</th>
                        <th>YEAR AVERAGE</th>
                        <th>Position</th>
                    </tr>
                </thead>
                <tbody>
                    ${SUBJECTS.map(subject => {
                        const grades = studentData.grades[subject] || Array(8).fill(0);
                        const averages = calculateAverages([...grades, ...grades.slice(0, 4)]);
                        return `
                            <tr>
                                <td>${subject}</td>
                                ${grades.map(grade => `<td>${grade}</td>`).join('')}
                                <td>${averages['AVERAGE (1st Semester)']}</td>
                                ${grades.slice(0, 4).map(grade => `<td>${grade}</td>`).join('')}
                                <td>${averages['AVERAGE (2nd Semester)']}</td>
                                <td>${averages['YEAR AVERAGE']}</td>
                                <td>${studentData.positions[subject] || '-'}</td>
                            </tr>
                        `;
                    }).join('')}
                </tbody>
            </table>
        </div>
    `;
    
    document.getElementById('reportCardPreview').classList.remove('hidden');
}

// Event listeners
document.addEventListener('DOMContentLoaded', () => {
    initializeGradeInputs();
    
    const generateBtn = document.getElementById('generateBtn');
    generateBtn.addEventListener('click', () => {
        const studentData = {
            name: document.getElementById('studentName').value,
            class: document.getElementById('className').value,
            term: document.getElementById('term').value,
            year: document.getElementById('year').value,
            grades: {},
            positions: {}
        };

        // Collect all grades
        document.querySelectorAll('.grade-input').forEach(input => {
            const subject = input.dataset.subject;
            const period = input.dataset.period;
            
            if (!studentData.grades[subject]) {
                studentData.grades[subject] = [];
            }
            
            const grade = parseFloat(input.value) || 0;
            const index = PERIODS.indexOf(period);
            studentData.grades[subject][index] = grade;
        });

        generateReportCard(studentData);
    });

    const printBtn = document.getElementById('printBtn');
    printBtn.addEventListener('click', () => window.print());
});
