# Bridge of Hope Girls' School Management System
## Offline Setup Instructions

### Problem
The system fails on computers without internet connection because it depends on external CDN resources. You'll see errors like:
- `GET https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css net::ERR_INTERNET_DISCONNECTED`
- `Uncaught ReferenceError: Chart is not defined`

### Solution: Make the System Work Offline

## Quick Setup (Recommended)

### Option 1: Automatic Setup (Windows)
1. **Run the download script** (requires internet connection):
   ```
   Right-click on "download-dependencies.bat" → "Run as administrator"
   ```
   OR
   ```
   Right-click on "download-dependencies.ps1" → "Run with PowerShell"
   ```

2. **Wait for download to complete** - this will create a `libs` folder with all required files

3. **Test the setup**:
   - Open `test-offline.html` in your browser
   - Verify all components are working
   - Disconnect from internet and test again

4. **Transfer the complete folder** to any computer - it will now work offline!

### Option 2: Manual Setup

#### Step 1: Create Directory Structure
Create a `libs` folder in your project directory:
```
SCHOOL MANAGEMENT SYSTEM/
├── libs/
│   ├── webfonts/
│   └── (downloaded files will go here)
├── index.html
├── script.js
└── (other files)
```

#### Step 2: Download Required Files
Download these files and save them in the `libs` folder:

**Bootstrap:**
- https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css → `libs/bootstrap.min.css`
- https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js → `libs/bootstrap.bundle.min.js`

**Font Awesome:**
- https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css → `libs/fontawesome.min.css`

**Font Awesome Fonts (save in `libs/webfonts/`):**
- https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2
- https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-regular-400.woff2
- https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-brands-400.woff2

**JavaScript Libraries:**
- https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js → `libs/jspdf.min.js`
- https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js → `libs/html2canvas.min.js`
- https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js → `libs/chart.min.js`

#### Step 3: Fix Font Awesome CSS
Edit `libs/fontawesome.min.css` and replace all instances of:
```
https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/
```
with:
```
./webfonts/
```

## Verification

### Test Offline Functionality
1. **Open `test-offline.html`** in your browser
2. **Check all sections**:
   - ✓ Bootstrap styling and components
   - ✓ Font Awesome icons display
   - ✓ Chart.js creates charts
   - ✓ PDF libraries are available
   - ✓ Connection status shows "Offline"

3. **Test the main system**:
   - Open `index.html`
   - Navigate through all tabs
   - Generate reports
   - Test printing functionality

### Troubleshooting

**If Bootstrap styling is missing:**
- Check if `libs/bootstrap.min.css` exists
- Verify file size is not 0 bytes

**If icons don't show:**
- Check if `libs/fontawesome.min.css` exists
- Verify `libs/webfonts/` folder contains font files
- Check if CSS paths were updated correctly

**If charts don't work:**
- Check if `libs/chart.min.js` exists
- Open browser console for error messages

**If PDF generation fails:**
- Check if both `libs/jspdf.min.js` and `libs/html2canvas.min.js` exist
- Use the Print → Save as PDF fallback method

## File Structure After Setup
```
SCHOOL MANAGEMENT SYSTEM/
├── libs/
│   ├── bootstrap.min.css
│   ├── bootstrap.bundle.min.js
│   ├── fontawesome.min.css
│   ├── jspdf.min.js
│   ├── html2canvas.min.js
│   ├── chart.min.js
│   └── webfonts/
│       ├── fa-solid-900.woff2
│       ├── fa-regular-400.woff2
│       └── fa-brands-400.woff2
├── index.html
├── script.js
├── auth.js
├── backup.js
├── test-offline.html
└── (other system files)
```

## Transfer to Other Computers
Once setup is complete:
1. **Copy the entire folder** to any computer
2. **No internet connection required**
3. **Open `index.html`** - everything will work offline
4. **All features available**: reports, printing, charts, etc.

## Benefits
- ✅ **Works completely offline**
- ✅ **Faster loading** (no CDN delays)
- ✅ **Reliable** (no dependency on external services)
- ✅ **Portable** (works on any computer)
- ✅ **Professional** (no broken layouts or missing features)

The system will now work perfectly on any computer, with or without internet connection!
