// Backup and Restore Functionality for School Grade Management System

// Global variables for backup history
let backupHistory = [];

// Initialize backup functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log("Backup system initializing...");
    
    // Load backup history from localStorage
    loadBackupHistory();
    
    // Initialize event listeners
    initializeBackupEventListeners();
    
    // Update current date and time
    updateBackupDateTime();
    
    // Set interval to update date and time every minute
    setInterval(updateBackupDateTime, 60000);
});

// Initialize event listeners for backup and restore
function initializeBackupEventListeners() {
    // Create backup button
    const createBackupBtn = document.getElementById('createBackupBtn');
    if (createBackupBtn) {
        createBackupBtn.addEventListener('click', createBackup);
    }
    
    // Restore file input change
    const restoreFile = document.getElementById('restoreFile');
    if (restoreFile) {
        restoreFile.addEventListener('change', handleRestoreFileChange);
    }
    
    // Restore backup button
    const restoreBackupBtn = document.getElementById('restoreBackupBtn');
    if (restoreBackupBtn) {
        restoreBackupBtn.addEventListener('click', restoreBackup);
    }
}

// Update backup date and time display
function updateBackupDateTime() {
    const backupDateTime = document.getElementById('backupDateTime');
    if (backupDateTime) {
        const now = new Date();
        backupDateTime.textContent = now.toLocaleString();
    }
}

// Create a backup of all system data
function createBackup() {
    try {
        // Show status message
        const backupStatus = document.getElementById('backupStatus');
        backupStatus.textContent = "Creating backup...";
        backupStatus.className = "alert alert-info mt-3";
        backupStatus.style.display = "block";
        
        // Get all system data
        const backupData = {
            timestamp: new Date().toISOString(),
            version: "1.0",
            schoolData: schoolData,
            studentsByYear: studentsByYear || {}
        };
        
        // Convert to JSON string
        const backupJson = JSON.stringify(backupData, null, 2);
        
        // Create filename with timestamp
        const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\..+/, '');
        const filename = `school_backup_${timestamp}.json`;
        
        // Create a blob and download link
        const blob = new Blob([backupJson], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        
        // Trigger download
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        // Add to backup history
        const backupRecord = {
            timestamp: new Date().toISOString(),
            filename: filename,
            size: formatBytes(backupJson.length),
            status: 'Completed'
        };
        
        backupHistory.unshift(backupRecord);
        saveBackupHistory();
        updateBackupHistoryTable();
        
        // Show success message
        backupStatus.textContent = `Backup created successfully! File: ${filename}`;
        backupStatus.className = "alert alert-success mt-3";
        
        console.log("Backup created successfully:", filename);
    } catch (error) {
        // Show error message
        const backupStatus = document.getElementById('backupStatus');
        backupStatus.textContent = `Error creating backup: ${error.message}`;
        backupStatus.className = "alert alert-danger mt-3";
        backupStatus.style.display = "block";
        
        console.error("Error creating backup:", error);
    }
}

// Handle restore file selection
function handleRestoreFileChange(event) {
    const file = event.target.files[0];
    const restoreFileDate = document.getElementById('restoreFileDate');
    const restoreBackupBtn = document.getElementById('restoreBackupBtn');
    
    if (file) {
        // Extract date from filename
        const filenameMatch = file.name.match(/school_backup_(\d{4}-\d{2}-\d{2}T\d{2}-\d{2}-\d{2})/);
        if (filenameMatch) {
            const backupDate = new Date(filenameMatch[1].replace(/-/g, ':'));
            restoreFileDate.textContent = backupDate.toLocaleString();
        } else {
            restoreFileDate.textContent = "Unknown date";
        }
        
        // Enable restore button
        restoreBackupBtn.disabled = false;
    } else {
        restoreFileDate.textContent = "No file selected";
        restoreBackupBtn.disabled = true;
    }
}

// Restore system data from backup file
function restoreBackup() {
    const restoreFile = document.getElementById('restoreFile');
    const restoreStatus = document.getElementById('restoreStatus');
    
    if (!restoreFile.files || restoreFile.files.length === 0) {
        restoreStatus.textContent = "Please select a backup file to restore.";
        restoreStatus.className = "alert alert-warning mt-3";
        restoreStatus.style.display = "block";
        return;
    }
    
    // Confirm restore
    if (!confirm("WARNING: This will replace all current system data with the data from the backup file. This action cannot be undone. Are you sure you want to continue?")) {
        return;
    }
    
    const file = restoreFile.files[0];
    const reader = new FileReader();
    
    // Show status message
    restoreStatus.textContent = "Restoring backup...";
    restoreStatus.className = "alert alert-info mt-3";
    restoreStatus.style.display = "block";
    
    reader.onload = function(e) {
        try {
            // Parse backup data
            const backupData = JSON.parse(e.target.result);
            
            // Validate backup data
            if (!backupData.schoolData) {
                throw new Error("Invalid backup file: Missing school data");
            }
            
            // Restore school data
            Object.assign(schoolData, backupData.schoolData);
            
            // Restore student data if available
            if (backupData.studentsByYear) {
                studentsByYear = backupData.studentsByYear;
                
                // Set current students to the current academic year if available
                if (schoolData.info.academicYear && studentsByYear[schoolData.info.academicYear]) {
                    currentStudents = studentsByYear[schoolData.info.academicYear];
                }
            }
            
            // Save restored data
            saveData();
            if (typeof saveDataToLocalStorage === 'function') {
                saveDataToLocalStorage();
            }
            
            // Update UI
            updateCounters();
            updateParentDropdown();
            updateParentStudentTable();
            
            // Add to backup history
            const backupRecord = {
                timestamp: new Date().toISOString(),
                filename: file.name,
                size: formatBytes(file.size),
                status: 'Restored'
            };
            
            backupHistory.unshift(backupRecord);
            saveBackupHistory();
            updateBackupHistoryTable();
            
            // Show success message
            restoreStatus.textContent = `Backup restored successfully! The system has been updated with data from: ${file.name}`;
            restoreStatus.className = "alert alert-success mt-3";
            
            // Reset file input
            restoreFile.value = '';
            document.getElementById('restoreFileDate').textContent = "No file selected";
            document.getElementById('restoreBackupBtn').disabled = true;
            
            console.log("Backup restored successfully:", file.name);
            
            // Reload page after a short delay to refresh all UI components
            setTimeout(() => {
                alert("Backup restored successfully. The page will now reload to apply all changes.");
                window.location.reload();
            }, 2000);
            
        } catch (error) {
            // Show error message
            restoreStatus.textContent = `Error restoring backup: ${error.message}`;
            restoreStatus.className = "alert alert-danger mt-3";
            
            console.error("Error restoring backup:", error);
        }
    };
    
    reader.onerror = function() {
        restoreStatus.textContent = "Error reading backup file.";
        restoreStatus.className = "alert alert-danger mt-3";
    };
    
    reader.readAsText(file);
}

// Load backup history from localStorage
function loadBackupHistory() {
    try {
        const savedHistory = localStorage.getItem('backupHistory');
        if (savedHistory) {
            backupHistory = JSON.parse(savedHistory);
            updateBackupHistoryTable();
        }
    } catch (error) {
        console.error("Error loading backup history:", error);
    }
}

// Save backup history to localStorage
function saveBackupHistory() {
    try {
        // Keep only the last 10 entries
        if (backupHistory.length > 10) {
            backupHistory = backupHistory.slice(0, 10);
        }
        
        localStorage.setItem('backupHistory', JSON.stringify(backupHistory));
    } catch (error) {
        console.error("Error saving backup history:", error);
    }
}

// Update backup history table
function updateBackupHistoryTable() {
    const backupHistoryTable = document.getElementById('backupHistoryTable');
    if (!backupHistoryTable) return;
    
    if (backupHistory.length === 0) {
        backupHistoryTable.innerHTML = `
            <tr>
                <td colspan="4" class="text-center">No backup history available</td>
            </tr>
        `;
        return;
    }
    
    backupHistoryTable.innerHTML = '';
    
    backupHistory.forEach(record => {
        const tr = document.createElement('tr');
        
        // Format date
        const date = new Date(record.timestamp);
        const formattedDate = date.toLocaleString();
        
        // Create status badge
        let statusBadge = '';
        if (record.status === 'Completed') {
            statusBadge = '<span class="badge bg-success">Completed</span>';
        } else if (record.status === 'Restored') {
            statusBadge = '<span class="badge bg-primary">Restored</span>';
        } else {
            statusBadge = `<span class="badge bg-secondary">${record.status}</span>`;
        }
        
        tr.innerHTML = `
            <td>${formattedDate}</td>
            <td>${record.filename}</td>
            <td>${record.size}</td>
            <td>${statusBadge}</td>
        `;
        
        backupHistoryTable.appendChild(tr);
    });
}

// Format bytes to human-readable size
function formatBytes(bytes, decimals = 2) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}
