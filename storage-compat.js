/**
 * Storage Compatibility Layer for Portable School Management System
 * Handles localStorage restrictions when running from file:// protocol
 */

(function() {
    'use strict';

    // Storage compatibility object
    window.StorageCompat = {
        // Check if localStorage is available and working
        isLocalStorageAvailable: function() {
            try {
                const test = '__storage_test__';
                localStorage.setItem(test, test);
                localStorage.removeItem(test);
                return true;
            } catch (e) {
                return false;
            }
        },

        // Check if sessionStorage is available and working
        isSessionStorageAvailable: function() {
            try {
                const test = '__session_test__';
                sessionStorage.setItem(test, test);
                sessionStorage.removeItem(test);
                return true;
            } catch (e) {
                return false;
            }
        },

        // Fallback storage using memory and cookies
        memoryStorage: {},
        
        // Cookie-based storage for persistence
        cookieStorage: {
            setItem: function(key, value) {
                try {
                    // Encode the value to handle special characters
                    const encodedValue = encodeURIComponent(value);
                    // Set cookie with 1 year expiration
                    const expires = new Date();
                    expires.setFullYear(expires.getFullYear() + 1);
                    document.cookie = `${key}=${encodedValue}; expires=${expires.toUTCString()}; path=/`;
                    return true;
                } catch (e) {
                    console.warn('Cookie storage failed:', e);
                    return false;
                }
            },
            
            getItem: function(key) {
                try {
                    const name = key + "=";
                    const decodedCookie = decodeURIComponent(document.cookie);
                    const ca = decodedCookie.split(';');
                    for (let i = 0; i < ca.length; i++) {
                        let c = ca[i];
                        while (c.charAt(0) === ' ') {
                            c = c.substring(1);
                        }
                        if (c.indexOf(name) === 0) {
                            return decodeURIComponent(c.substring(name.length, c.length));
                        }
                    }
                    return null;
                } catch (e) {
                    console.warn('Cookie retrieval failed:', e);
                    return null;
                }
            },
            
            removeItem: function(key) {
                try {
                    document.cookie = `${key}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                } catch (e) {
                    console.warn('Cookie removal failed:', e);
                }
            }
        },

        // Main storage interface
        setItem: function(key, value) {
            try {
                // Try localStorage first
                if (this.isLocalStorageAvailable()) {
                    localStorage.setItem(key, value);
                    return;
                }
                
                // Try sessionStorage
                if (this.isSessionStorageAvailable()) {
                    sessionStorage.setItem(key, value);
                    // Also store in memory for backup
                    this.memoryStorage[key] = value;
                    return;
                }
                
                // Try cookie storage for small data
                if (value.length < 3000) { // Cookie size limit
                    if (this.cookieStorage.setItem(key, value)) {
                        return;
                    }
                }
                
                // Fallback to memory storage
                this.memoryStorage[key] = value;
                console.warn('Using memory storage - data will be lost on page refresh');
                
            } catch (e) {
                console.error('Storage failed:', e);
                // Last resort - memory storage
                this.memoryStorage[key] = value;
            }
        },

        getItem: function(key) {
            try {
                // Try localStorage first
                if (this.isLocalStorageAvailable()) {
                    return localStorage.getItem(key);
                }
                
                // Try sessionStorage
                if (this.isSessionStorageAvailable()) {
                    const value = sessionStorage.getItem(key);
                    if (value !== null) return value;
                }
                
                // Try cookie storage
                const cookieValue = this.cookieStorage.getItem(key);
                if (cookieValue !== null) return cookieValue;
                
                // Fallback to memory storage
                return this.memoryStorage[key] || null;
                
            } catch (e) {
                console.error('Storage retrieval failed:', e);
                return this.memoryStorage[key] || null;
            }
        },

        removeItem: function(key) {
            try {
                // Remove from all possible storage locations
                if (this.isLocalStorageAvailable()) {
                    localStorage.removeItem(key);
                }
                
                if (this.isSessionStorageAvailable()) {
                    sessionStorage.removeItem(key);
                }
                
                this.cookieStorage.removeItem(key);
                delete this.memoryStorage[key];
                
            } catch (e) {
                console.error('Storage removal failed:', e);
                delete this.memoryStorage[key];
            }
        },

        // Clear all storage
        clear: function() {
            try {
                if (this.isLocalStorageAvailable()) {
                    localStorage.clear();
                }
                
                if (this.isSessionStorageAvailable()) {
                    sessionStorage.clear();
                }
                
                // Clear cookies (this is approximate)
                const cookies = document.cookie.split(";");
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i];
                    const eqPos = cookie.indexOf("=");
                    const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
                    this.cookieStorage.removeItem(name.trim());
                }
                
                this.memoryStorage = {};
                
            } catch (e) {
                console.error('Storage clear failed:', e);
                this.memoryStorage = {};
            }
        },

        // Get storage info for debugging
        getStorageInfo: function() {
            return {
                localStorage: this.isLocalStorageAvailable(),
                sessionStorage: this.isSessionStorageAvailable(),
                cookieEnabled: navigator.cookieEnabled,
                protocol: window.location.protocol,
                memoryKeys: Object.keys(this.memoryStorage).length
            };
        }
    };

    // Show storage status on load
    console.log('Storage Compatibility Layer loaded');
    console.log('Storage status:', window.StorageCompat.getStorageInfo());
    
    // Warn user if using limited storage
    if (!window.StorageCompat.isLocalStorageAvailable()) {
        console.warn('localStorage not available - using fallback storage methods');
        
        // Show user notification after DOM loads
        document.addEventListener('DOMContentLoaded', function() {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-warning alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 10px; right: 10px; z-index: 9999; max-width: 400px;';
            alertDiv.innerHTML = `
                <strong>Storage Notice:</strong> Running from file system. Data persistence may be limited. 
                For best results, use Firefox or run from a local server.
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // Auto-dismiss after 10 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 10000);
        });
    }

})();
