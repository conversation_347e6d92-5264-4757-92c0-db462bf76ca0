<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Promotion Status Feature Demo - Bridge of Hope Girls' School</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .demo-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .promotion-section {
            margin-bottom: 20px;
            text-align: center;
            border: 2px solid #000;
            padding: 15px;
            background-color: #f0f8ff;
        }
        .promotion-section h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
            font-weight: bold;
        }
        .promoted {
            color: #28a745;
        }
        .retained {
            color: #dc3545;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .criteria-box {
            background-color: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .grade-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 12px;
        }
        .grade-table th, .grade-table td {
            border: 1px solid #ddd;
            padding: 4px;
            text-align: center;
        }
        .grade-table th {
            background-color: #f4f4f4;
        }
        .failing-grade {
            color: red;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🎓 Promotion Status Feature Demo</h1>
    <p><strong>Bridge of Hope Girls' School - Dynamic Academic Standing System</strong></p>

    <div class="demo-container">
        <h2>📋 Current Promotion Criteria</h2>
        <div id="currentCriteria" class="criteria-box">Loading...</div>
        <button onclick="showCriteriaDetails()">View Detailed Criteria</button>
        <button onclick="testAllScenarios()">Test All Scenarios</button>
    </div>

    <div class="demo-container">
        <h2>✅ Example 1: Student with Excellent Performance (PROMOTED)</h2>
        <div id="excellentStudentDemo">
            <h4>Student: Alice Johnson - Grade Seven</h4>
            <table class="grade-table">
                <thead>
                    <tr>
                        <th>Subject</th>
                        <th>1st Sem Avg</th>
                        <th>2nd Sem Avg</th>
                        <th>Year Average</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>BIBLE</td><td>86</td><td>88</td><td>87</td><td>✅ Pass</td></tr>
                    <tr><td>ENGLISH</td><td>81</td><td>85</td><td>83</td><td>✅ Pass</td></tr>
                    <tr><td>MATHEMATICS</td><td>76</td><td>81</td><td>79</td><td>✅ Pass</td></tr>
                    <tr><td>GEOGRAPHY</td><td>78</td><td>82</td><td>80</td><td>✅ Pass</td></tr>
                    <tr><td>HISTORY</td><td>84</td><td>86</td><td>85</td><td>✅ Pass</td></tr>
                </tbody>
            </table>
            <div class="promotion-section">
                <h4>ACADEMIC STANDING</h4>
                <div style="font-size: 14px; font-weight: bold; color: #28a745;">
                    PROMOTED TO GRADE EIGHT
                </div>
                <div style="margin-top: 5px; font-size: 11px; color: #666;">
                    Overall Average: 83%
                </div>
            </div>
        </div>
    </div>

    <div class="demo-container">
        <h2>❌ Example 2: Student with Poor Performance (RETAINED)</h2>
        <div id="poorStudentDemo">
            <h4>Student: Bob Smith - Grade Seven</h4>
            <table class="grade-table">
                <thead>
                    <tr>
                        <th>Subject</th>
                        <th>1st Sem Avg</th>
                        <th>2nd Sem Avg</th>
                        <th>Year Average</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>BIBLE</td><td>66</td><td>68</td><td class="failing-grade">67</td><td>❌ Fail</td></tr>
                    <tr><td>ENGLISH</td><td>53</td><td>55</td><td class="failing-grade">54</td><td>❌ Fail (Core)</td></tr>
                    <tr><td>MATHEMATICS</td><td>46</td><td>51</td><td class="failing-grade">49</td><td>❌ Fail (Core)</td></tr>
                    <tr><td>GEOGRAPHY</td><td>63</td><td>66</td><td class="failing-grade">65</td><td>❌ Fail</td></tr>
                    <tr><td>HISTORY</td><td>59</td><td>61</td><td class="failing-grade">60</td><td>❌ Fail</td></tr>
                </tbody>
            </table>
            <div class="promotion-section">
                <h4>ACADEMIC STANDING</h4>
                <div style="font-size: 14px; font-weight: bold; color: #dc3545;">
                    RETAINED IN GRADE SEVEN
                </div>
                <div style="margin-top: 5px; font-size: 11px; color: #666;">
                    Overall Average: 59%
                </div>
                <div style="margin-top: 8px; font-size: 10px; color: #666; text-align: left;">
                    <strong>Reasons for retention:</strong>
                    <ul style="margin: 4px 0 0 20px; padding: 0;">
                        <li>Overall average (59%) below minimum required (70%)</li>
                        <li>Failed core subject(s): ENGLISH, MATHEMATICS</li>
                        <li>Too many failing subjects (5), maximum allowed: 2</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="demo-container">
        <h2>🎓 Example 3: Grade Twelve Student (GRADUATION)</h2>
        <div id="graduationDemo">
            <h4>Student: Carol Williams - Grade Twelve</h4>
            <table class="grade-table">
                <thead>
                    <tr>
                        <th>Subject</th>
                        <th>1st Sem Avg</th>
                        <th>2nd Sem Avg</th>
                        <th>Year Average</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <tr><td>BIBLE</td><td>88</td><td>90</td><td>89</td><td>✅ Pass</td></tr>
                    <tr><td>ENGLISH</td><td>85</td><td>87</td><td>86</td><td>✅ Pass</td></tr>
                    <tr><td>MATHEMATICS</td><td>82</td><td>85</td><td>84</td><td>✅ Pass</td></tr>
                    <tr><td>GEOGRAPHY</td><td>79</td><td>82</td><td>81</td><td>✅ Pass</td></tr>
                    <tr><td>HISTORY</td><td>86</td><td>88</td><td>87</td><td>✅ Pass</td></tr>
                </tbody>
            </table>
            <div class="promotion-section">
                <h4>ACADEMIC STANDING</h4>
                <div style="font-size: 14px; font-weight: bold; color: #28a745;">
                    QUALIFIED FOR GRADUATION
                </div>
                <div style="margin-top: 5px; font-size: 11px; color: #666;">
                    Overall Average: 85%
                </div>
            </div>
        </div>
    </div>

    <div class="demo-container">
        <h2>⚙️ Test Results</h2>
        <div id="testResults">
            <p>Click "Test All Scenarios" to run automated tests of the promotion status calculation.</p>
        </div>
    </div>

    <div class="demo-container">
        <h2>📖 How It Works</h2>
        <div class="criteria-box">
            <h4>Dynamic Promotion Logic:</h4>
            <ol>
                <li><strong>Grade Calculation:</strong> System calculates year averages for each subject</li>
                <li><strong>Overall Average:</strong> Computes student's overall academic performance</li>
                <li><strong>Core Subject Check:</strong> Ensures student passes critical subjects (Bible, English, Mathematics)</li>
                <li><strong>Failure Limit:</strong> Checks if student has too many failing subjects</li>
                <li><strong>Attendance Review:</strong> Considers attendance rate in promotion decision</li>
                <li><strong>Status Generation:</strong> Automatically displays "PROMOTED TO [NEXT GRADE]" or "RETAINED IN [CURRENT GRADE]"</li>
            </ol>
        </div>
    </div>

    <!-- Include the main script -->
    <script src="script.js"></script>
    
    <script>
        // Initialize demo data
        function initializeDemo() {
            // Ensure schoolData exists
            if (typeof schoolData === 'undefined') {
                window.schoolData = {
                    students: [],
                    grades: [],
                    promotionCriteria: null
                };
            }
            
            showCriteriaDetails();
        }

        function showCriteriaDetails() {
            try {
                const criteria = getBridgeOfHopePromotionCriteria();
                const criteriaHTML = `
                    <h4>Bridge of Hope Girls' School Promotion Standards (Updated):</h4>
                    <ul>
                        <li><strong>Minimum Passing Grade:</strong> ${criteria.minimumPassingGrade}% <span style="color: #dc3545;">(Raised from 60%)</span></li>
                        <li><strong>Minimum Overall Average:</strong> ${criteria.minimumOverallAverage}% <span style="color: #dc3545;">(Raised from 65%)</span></li>
                        <li><strong>Maximum Failing Subjects:</strong> ${criteria.maxFailingSubjects}</li>
                        <li><strong>Core Subjects (Must Pass):</strong> ${criteria.coreSubjects.join(', ')}</li>
                        <li><strong>Minimum Attendance Rate:</strong> ${criteria.minimumAttendanceRate}%</li>
                    </ul>
                    <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 4px; margin-top: 10px;">
                        <strong>⚠️ Important Change:</strong> Students now need an average of <strong>70% or higher</strong> to pass each subject and be promoted to the next grade level.
                    </div>
                `;
                document.getElementById('currentCriteria').innerHTML = criteriaHTML;
            } catch (error) {
                document.getElementById('currentCriteria').innerHTML = `
                    <p style="color: red;">Error loading criteria: ${error.message}</p>
                    <p>Please ensure the main script.js file is loaded properly.</p>
                `;
            }
        }

        function testAllScenarios() {
            try {
                const results = testPromotionStatus();
                
                let resultsHTML = '<h4>Automated Test Results:</h4>';
                
                // Test 1: Good Student
                resultsHTML += `
                    <div class="test-result">
                        <strong>Test 1 - Excellent Student:</strong><br>
                        Status: ${results.goodStudent.isPromoted ? '✅ PROMOTED' : '❌ RETAINED'}<br>
                        Overall Average: ${results.goodStudent.overallAverage}%<br>
                        Next Grade: ${results.goodStudent.nextGrade}<br>
                        Failing Subjects: ${results.goodStudent.failingSubjects}
                    </div>
                `;
                
                // Test 2: Poor Student
                resultsHTML += `
                    <div class="test-result">
                        <strong>Test 2 - Poor Performance Student:</strong><br>
                        Status: ${results.poorStudent.isPromoted ? '✅ PROMOTED' : '❌ RETAINED'}<br>
                        Overall Average: ${results.poorStudent.overallAverage}%<br>
                        Failing Subjects: ${results.poorStudent.failingSubjects}<br>
                        Core Subject Failures: ${results.poorStudent.coreSubjectFailures.join(', ') || 'None'}<br>
                        Reasons: ${results.poorStudent.reasons.join('; ')}
                    </div>
                `;
                
                // Test 3: Senior Student
                resultsHTML += `
                    <div class="test-result">
                        <strong>Test 3 - Grade Twelve Student:</strong><br>
                        Status: ${results.seniorStudent.isPromoted ? '🎓 QUALIFIED FOR GRADUATION' : '❌ RETAINED'}<br>
                        Overall Average: ${results.seniorStudent.overallAverage}%<br>
                        Next Level: ${results.seniorStudent.nextGrade}
                    </div>
                `;
                
                resultsHTML += '<p><strong>✅ All tests completed successfully!</strong></p>';
                
                document.getElementById('testResults').innerHTML = resultsHTML;
                
            } catch (error) {
                document.getElementById('testResults').innerHTML = `
                    <p style="color: red;">Error running tests: ${error.message}</p>
                    <p>Please ensure the main script.js file is loaded and all functions are available.</p>
                `;
            }
        }

        // Initialize when page loads
        window.addEventListener('load', initializeDemo);
    </script>
</body>
</html>
