// Login System for School Grade Management System

// User roles and credentials
const users = [
    {
        username: 'B<PERSON>',
        password: 'admin@BOH',
        role: 'admin',
        fullName: 'System Administrator'
    },
    {
        username: 'teacher',
        password: 'teacher123',
        role: 'teacher',
        fullName: 'Teacher Account'
    },
    {
        username: 'parent',
        password: 'parent123',
        role: 'parent',
        fullName: 'Parent Account'
    }
];

// Initialize the login system
document.addEventListener('DOMContentLoaded', function() {
    console.log("Login system initializing...");
    
    // Check if user is already logged in
    checkLoginStatus();
    
    // Add event listener to login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
});

// Handle login form submission
function handleLogin(event) {
    event.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const rememberMe = document.getElementById('rememberMe').checked;
    
    // Find user
    const user = users.find(u => u.username.toLowerCase() === username.toLowerCase() && u.password === password);
    
    if (user) {
        // Login successful
        loginUser(user, rememberMe);
    } else {
        // Login failed
        showLoginError();
    }
}

// Login user
function loginUser(user, rememberMe) {
    // Create user session
    const session = {
        username: user.username,
        role: user.role,
        fullName: user.fullName,
        loggedIn: true,
        loginTime: new Date().toISOString()
    };
    
    // Use StorageCompat if available, fallback to sessionStorage
    const storage = window.StorageCompat || sessionStorage;
    storage.setItem('userSession', JSON.stringify(session));

    // If remember me is checked, also try to store in localStorage
    if (rememberMe) {
        try {
            localStorage.setItem('userSession', JSON.stringify(session));
        } catch (e) {
            console.warn('Could not save to localStorage:', e);
        }
    }
    
    // Redirect to main page
    window.location.href = 'index.html';
}

// Check if user is already logged in
function checkLoginStatus() {
    const storage = window.StorageCompat || sessionStorage;
    let sessionData = storage.getItem('userSession');
    let session = sessionData ? JSON.parse(sessionData) : null;

    // If not found and StorageCompat is not available, check localStorage
    if (!session && !window.StorageCompat) {
        try {
            sessionData = localStorage.getItem('userSession');
            session = sessionData ? JSON.parse(sessionData) : null;

            // If found in localStorage, also set in sessionStorage
            if (session) {
                sessionStorage.setItem('userSession', JSON.stringify(session));
            }
        } catch (e) {
            console.warn('Could not access localStorage:', e);
        }
    }

    // If already logged in, redirect to main page
    if (session && session.loggedIn) {
        window.location.href = 'index.html';
    }
}

// Show login error
function showLoginError() {
    const loginAlert = document.getElementById('loginAlert');
    if (loginAlert) {
        loginAlert.classList.remove('d-none');
        
        // Clear error after 3 seconds
        setTimeout(() => {
            loginAlert.classList.add('d-none');
        }, 3000);
    }
    
    // Clear password field
    document.getElementById('password').value = '';
    document.getElementById('password').focus();
}
