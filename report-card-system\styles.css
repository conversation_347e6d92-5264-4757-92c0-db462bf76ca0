/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header styles */
header {
    text-align: center;
    margin-bottom: 40px;
    padding: 20px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo {
    max-width: 150px;
    height: auto;
    margin-bottom: 20px;
}

h1, h2 {
    color: #333;
}

/* Form styles */
.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 5px;
    color: #666;
}

input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Grades container */
.grades-section {
    margin: 40px 0;
}

.grade-row {
    display: flex;
    margin-bottom: 20px;
    padding: 15px;
    background-color: white;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.grade-label {
    width: 200px;
    font-weight: bold;
    color: #333;
}

.grade-inputs {
    flex: 1;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.grade-input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Buttons */
.btn-primary {
    background-color: #4CAF50;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

.btn-primary:hover {
    background-color: #45a049;
}

/* Report card preview */
.report-card-preview {
    margin-top: 40px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hidden {
    display: none;
}

/* Print styles */
@media print {
    .container {
        max-width: 100%;
        margin: 0;
        padding: 20px;
    }

    .report-card-preview {
        max-width: 100%;
        margin: 0;
    }

    .btn-primary {
        display: none;
    }

    .form-group {
        display: none;
    }
}
