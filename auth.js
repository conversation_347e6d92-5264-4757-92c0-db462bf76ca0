// Authentication and Role-Based Access Control for School Management System

// User roles and access levels
const roles = {
    admin: {
        name: 'Administrator',
        access: ['all']
    },
    teacher: {
        name: 'Teacher',
        access: ['dashboard', 'enterGrades', 'reports', 'attendance', 'curriculum', 'calendar']
    },
    parent: {
        name: 'Parent',
        access: ['dashboard', 'reports', 'attendance', 'calendar']
    }
};

// Initialize authentication system
document.addEventListener('DOMContentLoaded', function() {
    console.log("Authentication system initializing...");
    
    // Check if user is logged in
    checkAuthStatus();
    
    // Add event listener to logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
});

// Check authentication status
function checkAuthStatus() {
    // Use StorageCompat if available, fallback to sessionStorage
    const storage = window.StorageCompat || sessionStorage;
    const sessionData = storage.getItem('userSession');
    const session = sessionData ? JSON.parse(sessionData) : null;

    if (!session || !session.loggedIn) {
        // Not logged in, redirect to login page
        window.location.href = 'login.html';
        return;
    }
    
    // Update UI with user info
    updateUserInfo(session);
    
    // Apply role-based access control
    applyAccessControl(session.role);
}

// Update user info in UI
function updateUserInfo(session) {
    const userFullName = document.getElementById('userFullName');
    const userRole = document.getElementById('userRole');
    
    if (userFullName) {
        userFullName.textContent = session.fullName;
    }
    
    if (userRole) {
        userRole.textContent = roles[session.role]?.name || session.role;
    }
}

// Apply role-based access control
function applyAccessControl(userRole) {
    // Get user's access permissions
    const accessList = roles[userRole]?.access || [];
    const hasFullAccess = accessList.includes('all');
    
    // Admin-only elements
    const adminOnlyElements = document.querySelectorAll('.admin-only');
    adminOnlyElements.forEach(element => {
        if (userRole === 'admin' || hasFullAccess) {
            element.classList.remove('d-none');
        } else {
            element.classList.add('d-none');
        }
    });
    
    // Teacher and admin elements
    const teacherAdminOnlyElements = document.querySelectorAll('.teacher-admin-only');
    teacherAdminOnlyElements.forEach(element => {
        if (userRole === 'admin' || userRole === 'teacher' || hasFullAccess) {
            element.classList.remove('d-none');
        } else {
            element.classList.add('d-none');
        }
    });
    
    // Hide tabs not in access list
    const navItems = document.querySelectorAll('#mainNav .nav-item');
    navItems.forEach(item => {
        const link = item.querySelector('.nav-link');
        if (link) {
            const href = link.getAttribute('href');
            const tabId = href.substring(1); // Remove the # character
            
            if (!hasFullAccess && !accessList.includes(tabId)) {
                item.classList.add('d-none');
            }
        }
    });
}

// Handle logout
function handleLogout() {
    // Clear session data from all storage types
    const storage = window.StorageCompat || sessionStorage;
    storage.removeItem('userSession');

    // Also clear from localStorage if available
    try {
        localStorage.removeItem('userSession');
        sessionStorage.removeItem('userSession');
    } catch (e) {
        console.warn('Could not clear localStorage/sessionStorage:', e);
    }

    // Redirect to login page
    window.location.href = 'login.html';
}

// Check if user has permission for a specific action
function hasPermission(action) {
    const storage = window.StorageCompat || sessionStorage;
    const sessionData = storage.getItem('userSession');
    const session = sessionData ? JSON.parse(sessionData) : null;

    if (!session || !session.loggedIn) {
        return false;
    }
    
    const userRole = session.role;
    const accessList = roles[userRole]?.access || [];
    
    return accessList.includes('all') || accessList.includes(action);
}
