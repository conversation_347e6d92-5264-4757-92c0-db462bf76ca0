@echo off
echo Bridge of Hope Girls' School Management System
echo Downloading offline dependencies...
echo.

:: Create libs directory if it doesn't exist
if not exist "libs" mkdir libs

:: Download Bootstrap CSS
echo Downloading Bootstrap CSS...
powershell -Command "Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css' -OutFile 'libs\bootstrap.min.css'"

:: Download Bootstrap JS
echo Downloading Bootstrap JS...
powershell -Command "Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js' -OutFile 'libs\bootstrap.bundle.min.js'"

:: Download Font Awesome CSS
echo Downloading Font Awesome CSS...
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css' -OutFile 'libs\fontawesome.min.css'"

:: Download jsPDF
echo Downloading jsPDF...
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js' -OutFile 'libs\jspdf.min.js'"

:: Download html2canvas
echo Downloading html2canvas...
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js' -OutFile 'libs\html2canvas.min.js'"

:: Download Chart.js
echo Downloading Chart.js...
powershell -Command "Invoke-WebRequest -Uri 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js' -OutFile 'libs\chart.min.js'"

:: Create webfonts directory for Font Awesome
if not exist "libs\webfonts" mkdir libs\webfonts

:: Download Font Awesome fonts
echo Downloading Font Awesome fonts...
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-solid-900.woff2' -OutFile 'libs\webfonts\fa-solid-900.woff2'"
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-regular-400.woff2' -OutFile 'libs\webfonts\fa-regular-400.woff2'"
powershell -Command "Invoke-WebRequest -Uri 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/fa-brands-400.woff2' -OutFile 'libs\webfonts\fa-brands-400.woff2'"

echo.
echo All dependencies downloaded successfully!
echo.
echo Now fixing Font Awesome CSS paths...

:: Fix Font Awesome CSS to use local fonts
powershell -Command "(Get-Content 'libs\fontawesome.min.css') -replace 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/webfonts/', './webfonts/' | Set-Content 'libs\fontawesome.min.css'"

echo Font Awesome CSS paths fixed!
echo.
echo Setup complete! The system should now work offline.
echo.
pause
