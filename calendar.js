// Calendar and Event Management for School Grade Management System

// Initialize calendar UI
function initializeCalendarUI() {
    // Set default date to today
    const today = new Date().toISOString().split('T')[0];
    const eventDate = document.getElementById('eventDate');
    if (eventDate) {
        eventDate.value = today;
    }
    
    // Initialize calendar
    renderCalendar();
}

// Render calendar
function renderCalendar() {
    const calendarContainer = document.getElementById('schoolCalendar');
    if (!calendarContainer) return;
    
    // Get current date
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Render calendar for current month
    renderMonthCalendar(calendarContainer, currentMonth, currentYear);
}

// Render month calendar
function renderMonthCalendar(container, month, year) {
    // Clear container
    container.innerHTML = '';
    
    // Create calendar header
    const header = document.createElement('div');
    header.classList.add('d-flex', 'justify-content-between', 'align-items-center', 'mb-3');
    
    const monthNames = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    header.innerHTML = `
        <button class="btn btn-sm btn-outline-primary prev-month-btn">
            <i class="fas fa-chevron-left"></i> Previous
        </button>
        <h4 class="mb-0">${monthNames[month]} ${year}</h4>
        <button class="btn btn-sm btn-outline-primary next-month-btn">
            <i class="fas fa-chevron-right"></i> Next
        </button>
    `;
    
    container.appendChild(header);
    
    // Add event listeners to navigation buttons
    const prevMonthBtn = header.querySelector('.prev-month-btn');
    const nextMonthBtn = header.querySelector('.next-month-btn');
    
    prevMonthBtn.addEventListener('click', () => {
        let newMonth = month - 1;
        let newYear = year;
        
        if (newMonth < 0) {
            newMonth = 11;
            newYear--;
        }
        
        renderMonthCalendar(container, newMonth, newYear);
    });
    
    nextMonthBtn.addEventListener('click', () => {
        let newMonth = month + 1;
        let newYear = year;
        
        if (newMonth > 11) {
            newMonth = 0;
            newYear++;
        }
        
        renderMonthCalendar(container, newMonth, newYear);
    });
    
    // Create calendar grid
    const calendarGrid = document.createElement('div');
    calendarGrid.classList.add('calendar-grid');
    
    // Create table
    const table = document.createElement('table');
    table.classList.add('table', 'table-bordered');
    
    // Create header row with day names
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Sun</th>
            <th>Mon</th>
            <th>Tue</th>
            <th>Wed</th>
            <th>Thu</th>
            <th>Fri</th>
            <th>Sat</th>
        </tr>
    `;
    table.appendChild(thead);
    
    // Create body with date cells
    const tbody = document.createElement('tbody');
    
    // Get first day of month and number of days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    // Create calendar rows
    let date = 1;
    for (let i = 0; i < 6; i++) {
        // Create row
        const row = document.createElement('tr');
        
        // Create cells
        for (let j = 0; j < 7; j++) {
            const cell = document.createElement('td');
            cell.classList.add('calendar-cell');
            
            if (i === 0 && j < firstDay) {
                // Empty cell before first day of month
                cell.classList.add('empty-cell');
            } else if (date > daysInMonth) {
                // Empty cell after last day of month
                cell.classList.add('empty-cell');
            } else {
                // Date cell
                const dateStr = `${year}-${(month + 1).toString().padStart(2, '0')}-${date.toString().padStart(2, '0')}`;
                
                // Check if there are events on this date
                const eventsOnDate = schoolData.events.filter(event => event.date === dateStr);
                
                // Create date cell content
                cell.innerHTML = `
                    <div class="date-number">${date}</div>
                    <div class="events-container">
                        ${eventsOnDate.map(event => `
                            <div class="event-item event-${event.type}" data-event-id="${event.id}">
                                ${event.title}
                            </div>
                        `).join('')}
                    </div>
                `;
                
                // Highlight today
                const today = new Date();
                if (date === today.getDate() && month === today.getMonth() && year === today.getFullYear()) {
                    cell.classList.add('today');
                }
                
                // Add click event to show events
                if (eventsOnDate.length > 0) {
                    cell.addEventListener('click', () => showEventsModal(dateStr, eventsOnDate));
                }
                
                date++;
            }
            
            row.appendChild(cell);
        }
        
        tbody.appendChild(row);
        
        // Stop if we've used all days of the month
        if (date > daysInMonth) {
            break;
        }
    }
    
    table.appendChild(tbody);
    calendarGrid.appendChild(table);
    container.appendChild(calendarGrid);
    
    // Add event type legend
    const legend = document.createElement('div');
    legend.classList.add('calendar-legend', 'mt-3');
    legend.innerHTML = `
        <div class="d-flex flex-wrap">
            <div class="legend-item me-3">
                <span class="legend-color event-general"></span> General
            </div>
            <div class="legend-item me-3">
                <span class="legend-color event-exam"></span> Examination
            </div>
            <div class="legend-item me-3">
                <span class="legend-color event-holiday"></span> Holiday
            </div>
            <div class="legend-item me-3">
                <span class="legend-color event-meeting"></span> Meeting
            </div>
            <div class="legend-item">
                <span class="legend-color event-activity"></span> Activity
            </div>
        </div>
    `;
    
    container.appendChild(legend);
    
    // Add custom styles for calendar
    addCalendarStyles();
}

// Add calendar styles
function addCalendarStyles() {
    // Check if styles already exist
    if (document.getElementById('calendarStyles')) return;
    
    // Create style element
    const style = document.createElement('style');
    style.id = 'calendarStyles';
    style.textContent = `
        .calendar-cell {
            height: 100px;
            width: 14.28%;
            vertical-align: top;
            padding: 5px;
            position: relative;
        }
        
        .empty-cell {
            background-color: #f8f9fa;
        }
        
        .today {
            background-color: #e8f4ff;
        }
        
        .date-number {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .events-container {
            font-size: 0.8rem;
        }
        
        .event-item {
            padding: 2px 4px;
            margin-bottom: 2px;
            border-radius: 3px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
        }
        
        .event-general {
            background-color: #6c757d;
            color: white;
        }
        
        .event-exam {
            background-color: #dc3545;
            color: white;
        }
        
        .event-holiday {
            background-color: #28a745;
            color: white;
        }
        
        .event-meeting {
            background-color: #17a2b8;
            color: white;
        }
        
        .event-activity {
            background-color: #ffc107;
            color: black;
        }
        
        .calendar-legend {
            font-size: 0.8rem;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
        }
        
        .legend-color {
            display: inline-block;
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border-radius: 3px;
        }
    `;
    
    document.head.appendChild(style);
}

// Show events modal
function showEventsModal(date, events) {
    // Create modal for viewing events
    const modalId = 'viewEventsModal';
    let modal = document.getElementById(modalId);
    
    // Remove existing modal if it exists
    if (modal) {
        document.body.removeChild(modal);
    }
    
    // Format date for display
    const displayDate = new Date(date).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    // Create new modal
    modal = document.createElement('div');
    modal.id = modalId;
    modal.classList.add('modal', 'fade');
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'viewEventsModalLabel');
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewEventsModalLabel">Events on ${displayDate}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="list-group">
                        ${events.map(event => `
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1">${event.title}</h5>
                                    <small class="text-capitalize">${event.type}</small>
                                </div>
                                <p class="mb-1">${event.description || 'No description'}</p>
                                <div class="d-flex justify-content-end mt-2">
                                    <button class="btn btn-sm btn-outline-primary me-2 edit-event-btn" 
                                            data-event-id="${event.id}">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger remove-event-btn" 
                                            data-event-id="${event.id}">
                                        <i class="fas fa-trash-alt"></i> Remove
                                    </button>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Initialize modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // Add event listeners to edit and remove buttons
    const editButtons = modal.querySelectorAll('.edit-event-btn');
    editButtons.forEach(button => {
        button.addEventListener('click', handleEditEvent);
    });
    
    const removeButtons = modal.querySelectorAll('.remove-event-btn');
    removeButtons.forEach(button => {
        button.addEventListener('click', handleRemoveEvent);
    });
}

// Handle event form submit
function handleEventSubmit(event) {
    event.preventDefault();
    
    const title = document.getElementById('eventTitle').value;
    const date = document.getElementById('eventDate').value;
    const type = document.getElementById('eventType').value;
    const description = document.getElementById('eventDescription').value;
    
    if (!title || !date) {
        alert('Please enter event title and date.');
        return;
    }
    
    try {
        // Create event entry
        const eventEntry = {
            id: `event_${Date.now()}`,
            title: title,
            date: date,
            type: type,
            description: description,
            createdBy: JSON.parse(sessionStorage.getItem('userSession'))?.username || 'unknown',
            createdAt: new Date().toISOString(),
            lastModified: new Date().toISOString()
        };
        
        // Add to events array
        schoolData.events.push(eventEntry);
        
        // Save data
        saveData();
        
        // Clear form
        document.getElementById('eventTitle').value = '';
        document.getElementById('eventDescription').value = '';
        
        // Refresh calendar
        renderCalendar();
        
        alert('Event added successfully!');
    } catch (error) {
        console.error('Error adding event:', error);
        alert('Failed to add event. Error: ' + error.message);
    }
}

// Handle edit event
function handleEditEvent(event) {
    const eventId = event.currentTarget.dataset.eventId;
    const eventEntry = schoolData.events.find(e => e.id === eventId);
    
    if (!eventEntry) {
        alert('Event not found.');
        return;
    }
    
    // Create modal for editing
    const modalId = 'editEventModal';
    let modal = document.getElementById(modalId);
    
    // Remove existing modal if it exists
    if (modal) {
        document.body.removeChild(modal);
    }
    
    // Create new modal
    modal = document.createElement('div');
    modal.id = modalId;
    modal.classList.add('modal', 'fade');
    modal.setAttribute('tabindex', '-1');
    modal.setAttribute('aria-labelledby', 'editEventModalLabel');
    modal.setAttribute('aria-hidden', 'true');
    
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editEventModalLabel">Edit Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editEventForm">
                        <div class="mb-3">
                            <label for="editEventTitle" class="form-label">Event Title</label>
                            <input type="text" class="form-control" id="editEventTitle" value="${eventEntry.title}">
                        </div>
                        <div class="mb-3">
                            <label for="editEventDate" class="form-label">Date</label>
                            <input type="date" class="form-control" id="editEventDate" value="${eventEntry.date}">
                        </div>
                        <div class="mb-3">
                            <label for="editEventType" class="form-label">Event Type</label>
                            <select class="form-select" id="editEventType">
                                <option value="general" ${eventEntry.type === 'general' ? 'selected' : ''}>General</option>
                                <option value="exam" ${eventEntry.type === 'exam' ? 'selected' : ''}>Examination</option>
                                <option value="holiday" ${eventEntry.type === 'holiday' ? 'selected' : ''}>Holiday</option>
                                <option value="meeting" ${eventEntry.type === 'meeting' ? 'selected' : ''}>Meeting</option>
                                <option value="activity" ${eventEntry.type === 'activity' ? 'selected' : ''}>Activity</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="editEventDescription" class="form-label">Description</label>
                            <textarea class="form-control" id="editEventDescription" rows="3">${eventEntry.description}</textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEventBtn">Save Changes</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Initialize modal
    const modalInstance = new bootstrap.Modal(modal);
    modalInstance.show();
    
    // Add event listener to save button
    const saveBtn = document.getElementById('saveEventBtn');
    saveBtn.addEventListener('click', () => {
        const title = document.getElementById('editEventTitle').value;
        const date = document.getElementById('editEventDate').value;
        const type = document.getElementById('editEventType').value;
        const description = document.getElementById('editEventDescription').value;
        
        if (!title || !date) {
            alert('Please enter event title and date.');
            return;
        }
        
        // Update event
        const eventIndex = schoolData.events.findIndex(e => e.id === eventId);
        if (eventIndex !== -1) {
            schoolData.events[eventIndex].title = title;
            schoolData.events[eventIndex].date = date;
            schoolData.events[eventIndex].type = type;
            schoolData.events[eventIndex].description = description;
            schoolData.events[eventIndex].lastModified = new Date().toISOString();
            
            // Save data
            saveData();
            
            // Refresh calendar
            renderCalendar();
            
            // Hide modal
            modalInstance.hide();
            
            // Close the events modal too
            const eventsModal = bootstrap.Modal.getInstance(document.getElementById('viewEventsModal'));
            if (eventsModal) {
                eventsModal.hide();
            }
            
            alert('Event updated successfully!');
        }
    });
}

// Handle remove event
function handleRemoveEvent(event) {
    const eventId = event.currentTarget.dataset.eventId;
    
    if (confirm('Are you sure you want to remove this event?')) {
        // Remove event
        schoolData.events = schoolData.events.filter(e => e.id !== eventId);
        
        // Save data
        saveData();
        
        // Refresh calendar
        renderCalendar();
        
        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('viewEventsModal'));
        if (modal) {
            modal.hide();
        }
        
        alert('Event removed successfully!');
    }
}
